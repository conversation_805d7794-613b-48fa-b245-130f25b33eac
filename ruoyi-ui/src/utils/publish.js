class PubSub {
  constructor() {
    this.handlers = {};
  }

  subscribe(eventName, callback) {
    if (!this.handlers[eventName]) {
      this.handlers[eventName] = [];
    }
    this.handlers[eventName].push(callback);
  }

  unsubscribe(eventName, callback) {
    if (this.handlers[eventName]) {
      this.handlers[eventName] = this.handlers[eventName].filter(
        (handler) => handler !== callback
      );
    }
  }

  publish(eventName, ...args) {
    if (this.handlers[eventName]) {
      this.handlers[eventName].forEach((handler) => {
        handler(...args);
      });
    }
  }
}

const pubSub = new PubSub();

export default pubSub;
