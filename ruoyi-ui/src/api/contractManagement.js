import request from "@/utils/request";

export const uploadTemplate = (data) =>
  request({
    method: "post",
    url: "/api/templates/upload",
    headers: {
      "Content-Type": "multipart/form-data;charset=UTF-8",
    },
    data,
  });

export const saveTemplate = (data) =>
  request({
    method: "post",
    url: "/api/templates/save",
    data,
  });

export const saveTemplateParams = (data) =>
  request({
    method: "post",
    url: "/api/params",
    data,
  });

export const getTemplateList = (params) =>
  request({
    method: "get",
    url: "/api/templates",
    params,
  });

export const getTemplateDetail = (id) =>
  request({
    method: "get",
    url: `/api/templates/${id}`,
  });

export const getParamsList = (id) =>
  request({
    method: "get",
    url: `/api/params/templates/${id}`,
  });

export const removeParams = (id) =>
  request({
    method: "delete",
    url: `/api/params/${id}`,
  });

export const removeTemplate = (id) =>
  request({
    method: "delete",
    url: `/api/templates/${id}`,
  });

export const removeContract = (id) =>
  request({
    method: "delete",
    url: `/api/contracts/${id}`,
  });

export const initiateContract = (data) =>
  request({
    method: "post",
    url: "/api/contracts/initiate",
    data,
  });

export const getContractList = (params) =>
  request({
    method: "get",
    url: `/api/contracts/list`,
    params,
  });

export const getContractVersions = (contractId) =>
  request({
    method: "get",
    url: `/api/contracts/${contractId}/versions`,
  });

export const getContractDetail = (contractId) =>
  request({
    method: "get",
    url: `/api/contracts/${contractId}`,
  });

export const saveContractFile = (contractId, newStoragePath) =>
  request({
    method: "post",
    url: `/api/contracts/versions/${contractId}/save-callback`,
    params: {
      newStoragePath,
    },
  });

export const completeContract = (contractId) =>
  request({
    method: "put",
    url: `/api/contracts/${contractId}/status`,
    params: {
      status: "审核通过",
    },
  });

export const getMasterContractList = (params) =>
  request({
    method: "get",
    url: `/api/master-contracts`,
    params,
  });

export const updateMasterContract = (id, data) =>
  request({
    method: "put",
    url: `/api/master-contracts/${id}`,
    data,
  });

export const getMasterContractDetail = (id) =>
  request({
    method: "get",
    url: `/api/master-contracts/${id}`,
  });

export const getCommonValueList = (params) =>
  request({
    method: "get",
    url: `/api/common-values/page`,
    params,
  });

export const removeCommonValue = (id) =>
  request({
    method: "delete",
    url: `/api/common-values/${id}`,
  });

export const saveCommonValue = (data) =>
  request({
    method: "post",
    url: "/api/common-values",
    data,
  });

export const updateCommonValue = (data) =>
  request({
    method: "put",
    url: `/api/common-values/${data.id}`,
    data,
  });

export const getCommonValueListByQuery = (params) =>
  request({
    method: "get",
    url: `/api/common-values/query`,
    params,
  });
