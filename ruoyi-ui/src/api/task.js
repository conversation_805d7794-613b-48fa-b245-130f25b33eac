import request from "@/utils/request";

// 获取任务分页数据
export const getTaskPage = (params) =>
  request({
    method: "get",
    url: "/taskCenter/listByPage",
    params,
  });

// 通过任务模板创建任务
export const createTaskByTemplate = (data) => {
  const { list, ...template } = data;
  return request({
    method: "post",
    url: "/taskCenter/createByTemplate",
    data: {
      template,
      list,
    },
  });
};

// 创建临时任务
export const createTaskByTemp = (data) =>
  request({
    method: "post",
    url: "/taskCenter/createByTemp",
    data,
  });

// 通过任务单元创建任务
export const createTaskByUnit = (data) =>
  request({
    method: "post",
    url: "/taskCenter/createByUnit",
    data,
  });

// 获取任务
export const getTask = (params) =>
  request({
    method: "get",
    url: "/taskCenter/getById",
    params,
  });

// 完成任务
export const completeTask = (data) =>
  request({
    method: "post",
    url: "/taskCenter/taskComplete",
    data,
  });

// 更新任务描述
export const taskDescUpdate = (data) =>
  request({
    method: "post",
    url: "/taskCenter/taskDescUpdate",
    data,
  });

// 转派任务
export const transferTask = (data) =>
  request({
    method: "post",
    url: "/taskCenter/taskTransfer",
    data,
  });

// 批量转派任务
export const batchTransferTask = (data) =>
  request({
    method: "post",
    url: "/taskCenter/batchTaskTransfer",
    data,
  });

// 获取任务复核清单列表
export const getAuditTaskPage = (params) =>
  request({
    method: "get",
    url: "/taskCenter/auditList",
    params,
  });

// 复核任务
export const auditTask = (data) =>
  request({
    method: "post",
    url: "/taskCenter/taskAudit",
    data,
  });

export const batchTaskAudit = (data) =>
  request({
    method: "post",
    url: "/taskCenter/batchTaskAudit",
    data,
  });

// 获取附件列表
export const getTaskFileList = (params) =>
  request({
    method: "get",
    url: "/upload/fileList",
    params,
  });

// 删除附件
export const deleteFile = (params) =>
  request({
    method: "get",
    url: "/upload/delete",
    params,
  });

// 下载附件
export const downloadFile = (params) =>
  request({
    method: "get",
    url: "/upload/download",
    headers: {
      responseType: "blob",
    },
    responseType: "blob",
    params,
  });

// 上传附件
export const uploadFile = (data) =>
  request({
    method: "post",
    url: "/upload/file",
    headers: {
      "Content-Type": "multipart/form-data;charset=UTF-8",
    },
    data,
  });

export const uploadFileTrigger = (data) =>
  request({
    method: "post",
    url: "/upload/fileTrigger",
    headers: {
      "Content-Type": "multipart/form-data;charset=UTF-8",
    },
    data,
  });

export const taskNoExist = (data) =>
  request({
    method: "post",
    url: "/taskCenter/taskNoExist",
    data,
  });

export const multiTaskNoExist = (data) =>
  request({
    method: "post",
    url: "/taskCenter/multiTaskNoExist",
    data,
  });

export const taskHisCompleteDesc = (params) =>
  request({
    method: "Get",
    url: "/taskCenter/taskHisCompleteDesc",
    params,
  });

export const taskHisCheckDesc = (params) =>
  request({
    method: "Get",
    url: "/taskCenter/taskHisCheckDesc",
    params,
  });

export const rewriteTaskDesc = (data) =>
  request({
    method: "post",
    url: "/taskCenter/rewriteTaskDesc",
    data,
  });

export const tempDetail = (params, isLeader) =>
  request({
    method: "Get",
    url: isLeader ? "/system/leaderDash/tempDetail" : "/taskCenter/tempDetail",
    params,
  });

export const taskReset = (params) =>
  request({
    method: "Get",
    url: "/taskCenter/taskReset",
    params,
  });

export const taskFund = (params) =>
  request({
    method: "Get",
    url: "/system/fund/list",
    params,
  });

export const taskFundSave = (data) =>
  request({
    method: "Post",
    url: "/system/fund/save",
    data,
  });

export const taskFundRemove = (data) =>
  request({
    method: "Post",
    url: "/system/fund/delete",
    data,
  });

export const getThirdList = () =>
  request({
    method: "Get",
    url: "/taskCenter/thirdList",
  });

export const assignTask = (params) =>
  request({
    method: "Post",
    url: "/taskCenter/assignTask",
    params,
  });

export const signList = () =>
  request({
    method: "Get",
    url: "//indicator/temp/signList",
  });

// 导出明细
export const downloadFundFile = (params) =>
  request({
    method: "get",
    url: "/upload/downloadByIndicator",
    headers: {
      responseType: "blob",
    },
    responseType: "blob",
    params,
  });

// 导出日志
export const downloadLogs = (params, isLeader) =>
  request({
    method: "get",
    url: `/system/${isLeader ? "leaderDash" : "dashboard"}/exportExcel`,
    headers: {
      responseType: "blob",
    },
    responseType: "blob",
    params,
  });

export const getTradeDeptDetailExport = (params) =>
  request({
    method: "get",
    url: "/taskCockpit/getTradeDeptDetailExport",
    headers: {
      responseType: "blob",
    },
    responseType: "blob",
    params,
  });

export const getTradeDeptTableExport = (params) =>
  request({
    method: "get",
    url: "/taskCockpit/getTradeDeptTableExport",
    headers: {
      responseType: "blob",
    },
    responseType: "blob",
    params,
  });

export const editTaskEndTime = (data) =>
  request({
    method: "Post",
    url: "/taskCenter/editTaskEndTime",
    data,
  });

export const deleteTask = (params) =>
  request({
    method: "Get",
    url: "/taskCenter/deleteTask",
    params,
  });
