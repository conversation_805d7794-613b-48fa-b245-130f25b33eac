import { http } from "./requset.js";
import request from "@/utils/request";

import service from "./index";
// 获取实时数据信息
export function getRealTime() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/realTime/info`,
  });
}
// 添加站内/外链接
export function addQuickLink(data) {
  return request({
    method: "POST",
    url: '/api/user/links/add',
    data,
  });
}
// 获取站内/外链接
export function getQuickLinks(date) {
  return request({
    method: "GET",
    url: "/api/user/links",
    params: { date },
  });
}
// 删除链接
export function delQuickLink(id) {
  return request({
    method: "GET",
    url: `/api/user/links/deleted/${id}`,
  });
}

// // 添加站外链接
// export function addQuickLinkOut(data) {
//   return http.request({
//     method: "POST",
//     url: `${service.serviceContext}/quick-link/outstation`,
//     data,
//   });
// }
// // 获取站外链接
// export function getQuickLinkOut() {
//   return http.request({
//     method: "GET",
//     url: `${service.serviceContext}/quick-link/outstation`,
//   });
// }

// 站外可选链接
export function getCommonLink() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/quick-link/outstation/common`,
  });
}
// 新闻资讯列表
export function getNewsList() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/news/list`,
  });
}
// 新闻资讯已读
export function readNews(id) {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/news/read?id=${id}`,
  });
}
// 估值进度
export function getValuation() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/monitor/valuation`,
  });
}
// 结算进度
export function getSettlement() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/monitor/settlement`,
  });
}
