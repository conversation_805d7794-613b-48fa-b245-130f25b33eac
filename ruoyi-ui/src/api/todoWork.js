import { http } from "./requset.js";
import service from "./index";
import request from "@/utils/request";

/** 工作台-待办列表 */
export function getTodoList(params) {
  return request({
    method: "GET",
    url: "/system/dashboard/list",
    params,
  });
}
// 获取待办工作列表
// export function getTodoList (params) {
//   const { charger, toDoWork, title, complete, startDate, endDate } = params
//   let str = ''
//   if (startDate) {
//     str = `&startDate=${startDate}&endDate=${endDate}`
//   }
//   return http.request({
//     method: 'GET',
//     url: `${service.serviceContext}/personal/pending-work?charger=${charger}&toDoWork=${toDoWork}&title=${title}&complete=${complete}${str}`,
//   })
// }
// 操作待办工作状态
export function setTodo(data) {
  return http.request({
    method: "POST",
    url: `${service.serviceContext}/personal/complete-sign`,
    data,
  });
}
// 代办获取操作人
export function getOperators() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/personal/getOperators`,
  });
}
// 代办获取待办内容
export function getMenu() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/personal/getMenu`,
  });
}
// 代办提交
export function setWorkHandover(data) {
  return http.request({
    method: "POST",
    url: `${service.serviceContext}/personal/workHandover`,
    data,
  });
}
// 已完成的筛选项
export function getTasks() {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/personal/getTasks`,
  });
}
