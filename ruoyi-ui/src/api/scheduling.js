import { http } from './requset.js'
import service from './index'

// 获取人员
export function getSchedulingPersonnelsResponse() {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/scheduling/findUsers`,
  })
}

// 获取排班
export function getSchedulingsResponse(params) {
  return http.request({
    method: 'GET',
    url: `${service.serviceContext}/scheduling/list`,
    params,
  })
}

// 保存排班
export function saveScheduling(data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/scheduling/save`,
    data,
  })
}

// 删除排班
export function deleteScheduling(data) {
  return http.request({
    method: 'POST',
    url: `${service.serviceContext}/scheduling/del`,
    data,
  })
}