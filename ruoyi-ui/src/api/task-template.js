import request from "@/utils/request";

// 获取任务模板分页数据
export const getTaskTemplatePage = (params) =>
  request({
    method: "get",
    url: "/taskTemplate/listByPage",
    params,
  });

// 获取任务单元列表
export const getTaskTemplateList = (params) =>
  request({
    method: "get",
    url: "/taskTemplate/list",
    params,
  });

// 保存任务模板
export const saveTaskTemplate = (data) => {
  const { list, ...template } = data;
  return request({
    method: "post",
    url: "/taskTemplate/save",
    data: {
      template,
      list,
    },
  });
};

function listToTree(
  list,
  { idKey = "id", parentKey = "parentId", childrenKey = "children" } = {}
) {
  const tree = [];
  const lookup = {};

  // 第一步：创建一个以id为键的映射表
  list.forEach((item) => {
    lookup[item[idKey]] = { ...item, [childrenKey]: [] };
  });

  // 第二步：构建树结构
  list.forEach((item) => {
    const parentId = item[parentKey];
    const node = lookup[item[idKey]];
    if (parentId == "0" || !lookup[parentId]) {
      // 根节点
      tree.push(node);
    } else {
      // 子节点
      lookup[parentId][childrenKey].push(node);
    }
  });

  return tree;
}
// 获取任务模板
export const getTaskTemplate = (params) => {
  return request({
    method: "get",
    url: "/taskTemplate/detail",
    params,
  }).then((data) => {
    return {
      ...data.data.template,
      list: listToTree(data.data.list),
    };
  });
};

// 复制任务模板
export const copyTaskTemplate = (params) =>
  request({
    method: "get",
    url: "/taskTemplate/templateCopy",
    params,
  });

// 任务模板作废
export const cancelTaskTemplate = (params) =>
  request({
    method: "get",
    url: "/taskTemplate/editStatus",
    params,
  });

// excel导入任务模板
export const importTaskTemplate = (data) =>
  request({
    method: "post",
    url: "/taskTemplate/import",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });

// 删除任务模板
export const deleteTaskTemplate = (params) =>
  request({
    method: "get",
    url: "/taskTemplate/delete",
    params,
  });

// 通过任务模板创建任务
export const createTaskByTemplateId = (params) =>
  request({
    method: "get",
    url: "/taskCenter/createByTemplateId",
    params,
  });
