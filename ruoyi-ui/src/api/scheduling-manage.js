import request from "@/utils/request";

// 获取全部班次
export const getShiftList = (data) =>
  request({
    method: "post",
    url: "/sysShift/list",
    data,
  });

// 新增班次
export const saveShift = (data) =>
  request({
    method: "post",
    url: "/sysShift/save",
    data,
  });

// 更新班次
export const updateShift = (data) =>
  request({
    method: "post",
    url: "/sysShift/update",
    data,
  });

// 删除班次
export const removeShift = (id) =>
  request({
    method: "get",
    url: "/sysShift/del",
    params: { id },
  });

// 获取班次详情
export const getShiftDetail = (id) =>
  request({
    method: "get",
    url: "/sysShift/getById",
    params: { id },
  });

// 获取排班列表
export const getSchedulingList = (params) =>
  request({
    method: "get",
    url: "/sysArrange/list",
    params,
  });

// 根据组织获取班次
export const getShiftListByOrg = (params) =>
  request({
    method: "get",
    url: "/sysShift/getByOrgId",
    params,
  });

// 保存排班
export const saveScheduling = (data) =>
  request({
    method: "post",
    url: "/sysArrange/save",
    data,
  });

// 删除排班
export const removeScheduling = (id) =>
  request({
    method: "get",
    url: "/sysArrange/del",
    params: { id },
  });

// 获取节假日
export const getHoliday = () =>
  request({
    method: "get",
    url: "/calendar/holiday",
  });

// 覆盖排班
export const coverShift = (data) =>
  request({
    method: "post",
    url: "/sysArrange/cover",
    data,
  });

// 批量排班
export const multipleShift = (data) =>
  request({
    method: "post",
    url: "/sysArrange/batchScheduling",
    data,
  });
