import { http } from "./requset.js";
import request from "@/utils/request";
import service from "./index";
// import service from './index'
// 新增日程
export function addCalendar(data) {
  return request({
    method: "POST",
    url: `/taskCenter/createByTempByXh`,
    data,
  });
}

export function updateCalendar(data) {
  return request({
    method: "POST",
    url: `/taskCenter/updateSingleTask`,
    data,
  });
}

// 日历/待办/日程信息
export function getCalendar(startDate, endDate) {
  return request({
    method: "GET",
    url: `/taskCenter/todoListByDateRange`,
    params: {startDate, endDate},
  });
}
// 信息中心-日程/排班信息
export function getCalendarInfo(date) {
  return request({
    method: "GET",
    url: "/taskCenter/todoList",
    params: { date },
  });
}
// 获取日程
export function getMemo(startDate, endDate) {
  return http.request({
    method: "GET",
    url: `${service.serviceContext}/calendar/getMemo?startDate=${startDate}&endDate=${endDate}`,
  });
}
// 删除日程
export function deleteMemo(id) {
  return request({
    method: "GET",
    url: `/taskCenter/deleteTask?id=${id}`,
  });
}

// 获取当年所有节假日
export function getCalendarHolidays() {
  return request({
    method: "GET",
    url: `/calendar/holiday`,
  });
}

export function uploadCalendarHolidays(formData) {
  return http.request({
    method: "POST",
    url: `${service.serviceContext}/calendar/uploadHolidayInfo`,
    data: formData,
  });
}

export function getChangeShift(date) {
  return request({
    method: "GET",
    url: "/sysArrange/getChangeShift",
    params: { date },
  });
}

export function saveChangeShift(data) {
  return request({
    method: "POST",
    url: "/sysArrange/changeShift",
    data,
  });
}
