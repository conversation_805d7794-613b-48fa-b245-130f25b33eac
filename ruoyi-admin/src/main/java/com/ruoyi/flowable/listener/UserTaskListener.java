package com.ruoyi.flowable.listener;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.taskcenter.domain.*;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoService;
import com.ruoyi.web.taskcenter.service.OpsTaskTemplateService;
import com.ruoyi.web.taskcenter.service.OpsTaskAttrBasicReplicaService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.el.FixedValue;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户任务监听器
 *
 * <AUTHOR>
 * @since 2023/5/13
 */
@Slf4j
@Data
@Component("userTaskListener")
public class UserTaskListener implements TaskListener {

    /**
     * 注入字段（名称与流程设计时字段名称一致）
     */
    private FixedValue taskDescription;

    /**
     * 任务模板ID字段（从流程变量中获取）
     */
    private FixedValue templateId;

    private OpsTaskGenInfoService opsTaskGenInfoService;

    @Autowired
    public void setOpsTaskGenInfoService(OpsTaskGenInfoService opsTaskGenInfoService) {
        this.opsTaskGenInfoService = opsTaskGenInfoService;
    }

    private OpsTaskTemplateService taskTemplateService;

    @Autowired
    public void setTaskTemplateService(OpsTaskTemplateService taskTemplateService) {
        this.taskTemplateService = taskTemplateService;
    }

    private OpsTaskAttrBasicReplicaService replicaService;

    @Autowired
    public void setReplicaService(OpsTaskAttrBasicReplicaService replicaService) {
        this.replicaService = replicaService;
    }

    private ISysUserService iSysUserService;

    @Autowired
    public void setISysUserService(ISysUserService iSysUserService) {
        this.iSysUserService = iSysUserService;
    }

    private ISysDeptService iSysDeptService;

    @Autowired
    public void setISysDeptService(ISysDeptService iSysDeptService) {
        this.iSysDeptService = iSysDeptService;
    }

    /**
     * 无参构造函数，同时支持Spring Bean和Flowable反射创建
     */
    public UserTaskListener() {
        // 无参构造函数
    }

    /**
     * 获取任务生成信息服务（支持懒加载）
     */
    private OpsTaskGenInfoService getOpsTaskGenInfoService() {
        if (opsTaskGenInfoService == null) {
            opsTaskGenInfoService = SpringUtils.getBean(OpsTaskGenInfoService.class);
        }
        return opsTaskGenInfoService;
    }

    /**
     * 获取任务模板服务（支持懒加载）
     */
    private OpsTaskTemplateService getTaskTemplateService() {
        if (taskTemplateService == null) {
            taskTemplateService = SpringUtils.getBean(OpsTaskTemplateService.class);
        }
        return taskTemplateService;
    }

    /**
     * 获取任务副本服务（支持懒加载）
     */
    private OpsTaskAttrBasicReplicaService getReplicaService() {
        if (replicaService == null) {
            replicaService = SpringUtils.getBean(OpsTaskAttrBasicReplicaService.class);
        }
        return replicaService;
    }

    private ISysUserService getISysUserService() {
        if (iSysUserService == null) {
            iSysUserService = SpringUtils.getBean(ISysUserService.class);
        }
        return iSysUserService;
    }

    private ISysDeptService getISysDeptService() {
        if (iSysDeptService == null) {
            iSysDeptService = SpringUtils.getBean(ISysDeptService.class);
        }
        return iSysDeptService;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("执行任务监听器...");
//        log.info("任务ID: {}", delegateTask.getId());
//        log.info("任务名称: {}", delegateTask.getName());
//        log.info("任务定义Key: {}", delegateTask.getTaskDefinitionKey());
//        log.info("流程实例ID: {}", delegateTask.getProcessInstanceId());
//        log.info("执行ID: {}", delegateTask.getExecutionId());
//        log.info("流程定义ID: {}", delegateTask.getProcessDefinitionId());
//        log.info("委托人: {}", delegateTask.getAssignee());
//        log.info("所有者: {}", delegateTask.getOwner());
//        log.info("候选人: {}", delegateTask.getCandidates());
//        log.info("描述: {}", delegateTask.getDescription());
//        log.info("优先级: {}", delegateTask.getPriority());
//        log.info("创建时间: {}", delegateTask.getCreateTime());
//        log.info("到期时间: {}", delegateTask.getDueDate());
//        log.info("分类: {}", delegateTask.getCategory());
//        log.info("表单Key: {}", delegateTask.getFormKey());
//        log.info("租户ID: {}", delegateTask.getTenantId());
//        log.info("流程变量: {}", delegateTask.getVariables());
//        log.info("本地变量: {}", delegateTask.getVariablesLocal());
//        log.info("注入字段: {}", taskDescription != null ? taskDescription.getExpressionText() : "null");

        // 尝试通过任务模板创建任务
        tryCreateTaskFromTemplate(delegateTask);
    }

    /**
     * 尝试通过任务模板创建任务
     *
     * @param delegateTask 委托任务
     */
    private void tryCreateTaskFromTemplate(DelegateTask delegateTask) {
        try {
            // 方式1: 从注入字段获取模板ID
            String templateIdValue = getTemplateIdFromFixedValue();

            // 方式2: 从流程变量获取模板ID
            if (!StringUtils.hasText(templateIdValue)) {
                templateIdValue = getTemplateIdFromProcessVariables(delegateTask);
            }

            // 方式3: 从任务名称或任务定义Key推导模板ID
            if (!StringUtils.hasText(templateIdValue)) {
                templateIdValue = deriveTemplateIdFromTask(delegateTask);
            }

            if (StringUtils.hasText(templateIdValue)) {
                log.info("找到模板ID: {}, 开始创建任务...", templateIdValue);
                createTaskFromTemplate(templateIdValue, delegateTask);
            } else {
                log.warn("未找到有效的模板ID，跳过任务创建");
            }
        } catch (Exception e) {
            log.error("尝试通过模板创建任务时发生异常", e);
        }
    }

    /**
     * 从注入字段获取模板ID
     *
     * @return 模板ID
     */
    private String getTemplateIdFromFixedValue() {
        if (templateId != null && StringUtils.hasText(templateId.getExpressionText())) {
            return templateId.getExpressionText();
        }
        return null;
    }

    /**
     * 从流程变量获取模板ID
     *
     * @param delegateTask 委托任务
     * @return 模板ID
     */
    private String getTemplateIdFromProcessVariables(DelegateTask delegateTask) {
        Map<String, Object> variables = delegateTask.getVariables();
        if (variables != null) {
            // 尝试多种可能的变量名
            String[] possibleKeys = {"templateId", "template_id", "taskTemplateId", "task_template_id"};
            for (String key : possibleKeys) {
                Object value = variables.get(key);
                if (value != null && StringUtils.hasText(value.toString())) {
                    return value.toString();
                }
            }
        }
        return null;
    }

    /**
     * 从任务名称或定义Key推导模板ID
     *
     * @param delegateTask 委托任务
     * @return 模板ID
     */
    private String deriveTemplateIdFromTask(DelegateTask delegateTask) {
        // 这里可以根据业务规则从任务名称或定义Key推导模板ID
        // 例如：任务定义Key为 "userTask_template_123" 则模板ID为 "123"
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        if (StringUtils.hasText(taskDefKey) && taskDefKey.contains("template_")) {
            String[] parts = taskDefKey.split("template_");
            if (parts.length > 1) {
                return parts[1];
            }
        }
        return null;
    }

    /**
     * 通过任务模板创建任务
     *
     * @param templateIdValue 模板ID
     * @param delegateTask    委托任务（用于获取上下文信息）
     */
    private void createTaskFromTemplate(String templateIdValue, DelegateTask delegateTask) {
        try {
            // 1. 验证模板是否存在且有效
            OpsTaskTemplate template = getTaskTemplateService().getById(templateIdValue);
            if (template == null) {
                log.error("任务模板不存在，模板ID: {}", templateIdValue);
                return;
            }

            // 2. 获取模板关联的任务副本列表
            List<OpsTaskAttrBasicReplica> replicaList = getReplicaService().viewList(templateIdValue);
            if (replicaList == null || replicaList.isEmpty()) {
                log.warn("模板下没有任务副本，模板ID: {}", templateIdValue);
                return;
            }

            // 3. 获取流程任务的候选人和委托人信息
            Set<IdentityLink> candidates = delegateTask.getCandidates();
            String assignee = delegateTask.getAssignee();
            log.info("候选人: {}, 委托人: {}", candidates, assignee);

            // 4. 更新任务副本的权限信息
            updateTaskOwnerInfo(replicaList, candidates, assignee);

            // 3. 构建TemplateVO
            TemplateVO templateVO = new TemplateVO();
            templateVO.setTemplate(template);
            templateVO.setList(replicaList);

            // 4. 根据模板的调度类型决定创建方式
            String schedulerType = template.getSchedulerType();
            if ("manual".equals(schedulerType)) {
                // 立即创建任务
                getOpsTaskGenInfoService().createTaskByTemplate(templateVO, false);
                log.info("通过模板立即创建任务成功，模板ID: {}, 模板名称: {}", templateIdValue, template.getTemplateName());
            } else {
                // 其他类型（daily、dynamic）创建关联关系
                getOpsTaskGenInfoService().createTaskByTemplate(templateVO, false);
                log.info("通过模板创建任务关联关系成功，模板ID: {}, 调度类型: {}", templateIdValue, schedulerType);
            }

            // 5. 记录创建日志到流程变量（可选）
            delegateTask.setVariable("taskCreatedFromTemplate", true);
            delegateTask.setVariable("templateIdUsed", templateIdValue);
            delegateTask.setVariable("templateName", template.getTemplateName());

        } catch (Exception e) {
            log.error("通过模板创建任务失败，模板ID: {}", templateIdValue, e);
            // 可以选择抛出异常中断流程，或者继续执行
            // throw new RuntimeException("任务模板创建失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据流程权限信息更新任务副本的权限设置
     * 这些设置会在创建OpsTaskGenInfo时被继承到taskOwnerType和taskOwnerId字段
     *
     * @param replicaList 任务副本列表
     * @param candidates 候选人信息
     * @param assignee 委托人
     */
    private void updateTaskOwnerInfo(List<OpsTaskAttrBasicReplica> replicaList, Set<IdentityLink> candidates, String assignee) {
        for (OpsTaskAttrBasicReplica replica : replicaList) {
            // 如果有候选人组，说明是部门分配
            if (candidates != null && !candidates.isEmpty()) {
                // 查找候选组
                for (IdentityLink candidate : candidates) {
                    if ("candidate".equals(candidate.getType()) && StringUtils.hasText(candidate.getGroupId())) {
                        replica.setTaskOwnerType("1"); // 部门
                        replica.setTaskOwnerId(candidate.getGroupId());
                        SysDept sysDept = getISysDeptService().selectDeptById(Long.valueOf(candidate.getGroupId()));
                        if (sysDept != null) {
                            String deptName = sysDept.getDeptName();
                            replica.setTaskOwnerVal(deptName); // 可以后续通过组ID获取组名
                        }
                        log.info("任务副本 {} 设置为部门，候选组: {}", replica.getId(), candidate.getGroupId());
                        break; // 找到第一个有效的候选组即可
                    }
                }
            }
            // 如果没有候选人组，但有委托人，说明是具体人员分配
            else if (StringUtils.hasText(assignee)) {
                replica.setTaskOwnerType("2"); // 具体人员
                replica.setTaskOwnerId(assignee);
                SysUser sysUser = getISysUserService().selectUserById(Long.valueOf(assignee));
                if (sysUser != null) {
                    replica.setTaskOwnerVal(sysUser.getUserName()); // 可以后续通过用户ID获取用户名
                }
                log.info("任务副本 {} 设置为具体人员，委托人: {}", replica.getId(), assignee);
            }
            // 都没有的情况下，记录警告
            else {
                log.warn("任务副本 {} 无法获取有效的权限信息，委托人: {}, 候选人: {}", replica.getId(), assignee, candidates);
            }
            LocalDate today = LocalDate.now();
            LocalDateTime startDateTime = today.atTime(8, 0, 0);
            LocalDateTime endDateTime = today.atTime(22, 0, 0);
            ZoneId zoneId = ZoneId.of("Asia/Shanghai");
            ZonedDateTime startzonedDateTime = startDateTime.atZone(zoneId);
            ZonedDateTime endzonedDateTime = endDateTime.atZone(zoneId);
            Date starDate = Date.from(startzonedDateTime.toInstant());
            Date endDate = Date.from(endzonedDateTime.toInstant());
            replica.setTaskStartTime(starDate);
            replica.setTaskEndTime(endDate);
        }
    }


}
