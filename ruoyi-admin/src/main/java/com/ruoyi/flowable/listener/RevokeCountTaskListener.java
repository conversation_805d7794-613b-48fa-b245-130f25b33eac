package com.ruoyi.flowable.listener;

import com.ruoyi.common.utils.spring.SpringUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 撤回次数计算任务监听器
 * 用于在任务触发时对计数器进行累加，并将结果设置为流程变量
 *
 * <AUTHOR>
 */
@Slf4j
public class RevokeCountTaskListener implements TaskListener {

    private RuntimeService runtimeService;

    /**
     * 计数器变量名字段（通过流程设计器配置）
     * 默认值：revokeCount
     */
    @Setter
    private org.flowable.common.engine.api.delegate.Expression variableName;

    /**
     * 每次触发的增量值字段（通过流程设计器配置）
     * 默认值：1
     */
    @Setter
    private org.flowable.common.engine.api.delegate.Expression incrementValue;

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("计数器监听器触发，任务: {}", delegateTask.getName());

        try {
            // 只在任务创建时执行
            if (!EVENTNAME_CREATE.equals(delegateTask.getEventName())) {
                log.debug("非任务创建事件，跳过计数器累加");
                return;
            }

            // 获取变量名
            String varName = getFieldValue(variableName, delegateTask);
            if (varName == null || varName.trim().isEmpty()) {
                varName = "revokeCount"; // 默认变量名
            }

            // 获取增量值
            int increment = getIncrementValue(delegateTask);

            // 获取当前计数器值
            Integer currentCount = (Integer) getRuntimeService().getVariable(
                delegateTask.getProcessInstanceId(), varName);

            // 如果变量不存在，初始化为1
            if (currentCount == null) {
                currentCount = 0;
            }

            // 计算新的计数器值
            int newCount = currentCount + increment;

            // 设置流程变量
            getRuntimeService().setVariable(delegateTask.getProcessInstanceId(), varName, newCount);

            log.info("计数器累加完成，任务: {}, 变量名: {}, 原值: {}, 增量: {}, 新值: {}",
                delegateTask.getName(), varName, currentCount, increment, newCount);

        } catch (Exception e) {
            log.error("计数器累加时发生异常，任务: {}", delegateTask.getName(), e);
            // 不抛出异常，避免影响正常流程
        }
    }

    /**
     * 获取增量值
     */
    private int getIncrementValue(DelegateTask delegateTask) {
        try {
            String incrementStr = getFieldValue(incrementValue, delegateTask);
            if (incrementStr != null && !incrementStr.trim().isEmpty()) {
                return Integer.parseInt(incrementStr.trim());
            }
        } catch (NumberFormatException e) {
            log.warn("增量值配置错误，使用默认值1: {}", e.getMessage());
        }
        return 1; // 默认增量值为1
    }

    /**
     * 获取字段值
     */
    private String getFieldValue(org.flowable.common.engine.api.delegate.Expression expression, DelegateTask delegateTask) {
        if (expression == null) {
            return null;
        }
        try {
            Object value = expression.getValue(delegateTask);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("获取字段值失败", e);
            return null;
        }
    }

    /**
     * 获取RuntimeService
     */
    private RuntimeService getRuntimeService() {
        if (runtimeService == null) {
            try {
                runtimeService = SpringUtils.getBean(RuntimeService.class);
            } catch (Exception e) {
                log.error("无法获取RuntimeService", e);
                throw new RuntimeException("无法获取RuntimeService", e);
            }
        }
        return runtimeService;
    }
}
