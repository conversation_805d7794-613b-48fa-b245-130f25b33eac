package com.ruoyi.shift.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.shift.domain.dto.OpsSysArrangeDTO;
import com.ruoyi.shift.domain.entity.OpsSysArrange;
import com.ruoyi.shift.domain.vo.OpsSysArrangeVO;
import liquibase.pro.packaged.P;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * &#064;date 2024-07-30-19:35
 */

@Mapper
public interface OpsSysArrangeMapper extends BaseMapper<OpsSysArrange> {


    Integer findByShiftId(@Param("id") String id);

    List<OpsSysArrangeVO> list(@Param("id") String id, @Param("startDate") String startDate, @Param("endDate") String endDate);

    void del(@Param("opsSysArrange") OpsSysArrangeDTO opsSysArrange);

    Integer findByOrgShiftUserDate(@Param("opsSysArrange") OpsSysArrange opsSysArrange);

    void delByDate(@Param("opsSysArrangedDTO") OpsSysArrangeDTO opsSysArrangedDTO);

    List<OpsSysArrangeVO> findByUserDate(@Param("weekStart") LocalDate weekStart, @Param("weekEnd") LocalDate weekEnd, @Param("userId") Long userId);

    List<OpsSysArrangeVO> getChangeShiftToday(@Param("date") String date, @Param("userId") Long userId);

    List<OpsSysArrangeVO> getChangeShiftLastDay(@Param("date") String date);

}
