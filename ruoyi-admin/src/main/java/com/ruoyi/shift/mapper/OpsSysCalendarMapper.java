package com.ruoyi.shift.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.shift.domain.entity.OpsSysCalendar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:19
 */
@Mapper
public interface OpsSysCalendarMapper extends BaseMapper<OpsSysCalendar> {

    @Select("SELECT CALENDAR_DATE, \n" +
            "       NUMS \n" +
            "  FROM (select CALENDAR_DATE,\n" +
            "               TRADE,\n" +
            "               ROWNUM AS NUMS\n" +
            "          from OPS_SYS_CALENDAR\n" +
            "         where CALENDAR_DATE > #{today}\n" +
            "           and CALENDAR_DATE <#{moDay}\n" +
            "           and TRADE='Y' order by CALENDAR_DATE) \n" +
            " WHERE NUMS=#{df}")
    OpsSysCalendar selectStringDate(@Param("today") String today, @Param("moDay") String moDay, @Param("df") Integer df);

    @Select("SELECT CALENDAR_DATE FROM OPS_SYS_CALENDAR WHERE TRADE = 'Y' AND CALENDAR_DATE >= #{startDate} AND CALENDAR_DATE <= #{endDate}")
    List<String> listByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select("SELECT CALENDAR_DATE from (select ROWNUM,c1.CALENDAR_DATE \n" +
            "  from (select \n" +
            "               CALENDAR_DATE\n" +
            "          from OPS_SYS_CALENDAR\n" +
            "         where CALENDAR_DATE >=#{rangeDate}\n" +
            "           and CALENDAR_DATE<=#{date}\n" +
            "           and TRADE='Y' ORDER by CALENDAR_DATE desc) c1  ) c2 where c2.ROWNUM=2")
    String listRowNumberFixed(@Param("rangeDate") String rangeDate, @Param("date") String date);

    @Select("SELECT CALENDAR_DATE from (select ROWNUM,c1.CALENDAR_DATE  \n" +
            "              from (select  \n" +
            "                           CALENDAR_DATE \n" +
            "                      from OPS_SYS_CALENDAR \n" +
            "                     where CALENDAR_DATE <=#{range} \n" +
            "                       and CALENDAR_DATE>=#{today} \n" +
            "                       and TRADE='Y' ORDER by CALENDAR_DATE ) c1  ) c2 where c2.ROWNUM=2")
    String nextWorkdayFixed(@Param("today") String today, @Param("range") String rangeDate);

    @Select("SELECT CALENDAR_DATE from (select ROWNUM,c1.CALENDAR_DATE  \n" +
            "              from (select  \n" +
            "                           CALENDAR_DATE \n" +
            "                      from OPS_SYS_CALENDAR \n" +
            "                     where CALENDAR_DATE <=#{range} \n" +
            "                       and CALENDAR_DATE>=#{today} \n" +
            "                       and TRADE='Y' ORDER by CALENDAR_DATE ) c1  ) c2 where c2.ROWNUM=#{of}")
    String nextWorkday(@Param("today") String day,@Param("range") String rangeDate, @Param("of") Integer offset);
    @Select("SELECT CALENDAR_DATE from (select ROWNUM,c1.CALENDAR_DATE \n" +
            "  from (select \n" +
            "               CALENDAR_DATE\n" +
            "          from OPS_SYS_CALENDAR\n" +
            "         where CALENDAR_DATE >=#{rangeDate}\n" +
            "           and CALENDAR_DATE<=#{date}\n" +
            "           and TRADE='Y' ORDER by CALENDAR_DATE desc) c1  ) c2 where c2.ROWNUM=#{of}")
    String listRowNumber(@Param("date") String day, @Param("rangeDate") String rangeDate, @Param("of") int abs);
}
