package com.ruoyi.shift.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/7/23 14:14
 */
@TableName(value = "ops_sys_calendar")
@Data
@Accessors(chain = true)
public class OpsSysCalendar implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 日历日期
     */
    private String calendarDate;

    /**
     * 是否是交易日：Y、N
     */
    private String trade;

    /**
     * 今天是那年
     */
    private String yearVal;

    /**
     * 今天是那个月
     */
    private String monthVal;

    /**
     * 今天是第几周
     */
    private String weekVal;

    /**
     * 今天是周几
     */
    private String dayWeek;

    /**
     * 是否是节假日  0 否 1 是
     */
    private String holiday;

    /**
     * 时间戳
     */
    private Date  dateTime;
} 