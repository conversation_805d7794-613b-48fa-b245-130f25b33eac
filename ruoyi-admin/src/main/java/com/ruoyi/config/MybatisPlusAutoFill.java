package com.ruoyi.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;

import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
@Component
public class MybatisPlusAutoFill implements MetaObjectHandler {


    //设置数据新增时候的，字段自动赋值规则
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("执行自动填充 - INSERT，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());

        this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updatedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "editedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "uploadedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "createTime", Date.class,new Date());

        // 获取用户信息
        Long userId = getLoginId()==null? -1L : Long.parseLong(getLoginId());
        String userIdStr = userId.toString();
        String userName = getLoginUsername()==null? "" : getLoginUsername();

        log.debug("自动填充用户信息 - userId: {}, userName: {}", userId, userName);

        // 填充字段，注意类型匹配 - 使用fillStrategy强制填充
        this.fillStrategy(metaObject, "createBy", userIdStr);
        this.fillStrategy(metaObject, "creatorId", userId);  // Long类型
        this.fillStrategy(metaObject, "creatorName", userName);
        this.fillStrategy(metaObject, "uploaderId", userId);  // Long类型
        this.fillStrategy(metaObject, "editorId", userId);  // Long类型
        this.fillStrategy(metaObject, "updateBy", userIdStr);

        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();
                Date current = ObjectUtil.isNotNull(baseEntity.getCreateTime())
                    ? baseEntity.getCreateTime() : new Date();
                baseEntity.setCreateTime(current);
                baseEntity.setUpdateTime(current);
                String username = StringUtils.isNotBlank(baseEntity.getCreateBy())
                    ? baseEntity.getCreateBy() : getLoginUsername();
                // 当前已登录 且 创建人为空 则填充
                baseEntity.setCreateBy(username);
                // 当前已登录 且 更新人为空 则填充
                baseEntity.setUpdateBy(username);
            }
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
        }

        log.debug("自动填充完成");




    }

    private String getLoginUsername() {
        LoginUser loginUser;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
        return ObjectUtil.isNotNull(loginUser) ? loginUser.getUsername() : null;
    }

    private String getLoginId() {
        LoginUser loginUser;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
        return ObjectUtil.isNotNull(loginUser) ? String.valueOf(loginUser.getUserId()): null;
    }

    //设置数据修改update时候的，字段自动赋值规则
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("执行自动填充 - UPDATE，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());

        this.strictUpdateFill(metaObject, "updatedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "editedTime", LocalDateTime.class, LocalDateTime.now());

        // 获取用户信息
        Long userId = getLoginId()==null? -1L : Long.parseLong(getLoginId());
        String userIdStr = userId.toString();
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updateBy", String.class, userIdStr);
        this.strictUpdateFill(metaObject, "editorId", Long.class, userId);  // Long类型

        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();
                Date current = new Date();
                // 更新时间填充(不管为不为空)
                baseEntity.setUpdateTime(current);
                String username = getLoginUsername();
                // 当前已登录 更新人填充(不管为不为空)
                if (StringUtils.isNotBlank(username)) {
                    baseEntity.setUpdateBy(username);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
        }

        log.debug("UPDATE自动填充完成");
    }

    public static boolean isWeb() {
        return RequestContextHolder.getRequestAttributes() != null;
    }


}
