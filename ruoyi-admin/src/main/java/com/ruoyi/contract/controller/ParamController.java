package com.ruoyi.contract.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.contract.domain.ParameterValue;
import com.ruoyi.contract.domain.dto.ParamSetDTO;
import com.ruoyi.contract.domain.vo.ParamSetVO;
import com.ruoyi.contract.service.ParamService;
import com.ruoyi.contract.service.ParameterValueService;

import com.ruoyi.web.taskcenter.util.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数管理Controller
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Api(tags = "参数管理")
@Slf4j
@RestController
@RequestMapping("/api/params")
@RequiredArgsConstructor
public class ParamController extends BaseController {

    private final ParamService paramService;
    private final ParameterValueService parameterValueService;

    /**
     * 获取模板的所有参数集（包含参数列表）
     */
    @ApiOperation("获取模板的所有参数集")
    @GetMapping("/templates/{templateId}")
    public AjaxResult getParamSetsByTemplate(@ApiParam("模板ID") @PathVariable Long templateId) {
        try {
            List<ParamSetVO> paramSets = paramService.listParamSetsWithParamsByTemplate(templateId);
            return AjaxResult.success(paramSets);

        } catch (Exception e) {
            log.error("获取参数集列表失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取参数集详情
     */
    @ApiOperation("获取参数集详情")
    @GetMapping("/{setId}")
    public AjaxResult getParamSetDetails(@ApiParam("参数集ID") @PathVariable Long setId) {
        try {
            ParamSetDTO paramSetDTO = paramService.getParamSetDetails(setId);
            return AjaxResult.success(paramSetDTO);

        } catch (Exception e) {
            log.error("获取参数集详情失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 保存或更新参数集
     */
    @ApiOperation("保存或更新参数集")
    @PostMapping
    public AjaxResult saveOrUpdateParamSet(@ApiParam("参数集信息") @RequestBody @Validated ParamSetDTO dto) {
        try {
            paramService.saveOrUpdateParamSet(dto);
            return AjaxResult.success("保存成功");

        } catch (Exception e) {
            log.error("保存参数集失败", e);
            return AjaxResult.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除参数集
     */
    @ApiOperation("删除参数集")
    @DeleteMapping("/{setId}")
    public AjaxResult deleteParamSet(@ApiParam("参数集ID") @PathVariable Long setId) {
        try {
            paramService.deleteParamSet(setId);
            return AjaxResult.success("删除成功");

        } catch (Exception e) {
            log.error("删除参数集失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取参数的历史值
     */
    @ApiOperation("获取参数的历史值")
    @GetMapping("/history-values")
    public AjaxResult getHistoryValues(@ApiParam("参数名") @RequestParam String paramName) {
        try {
            List<ParameterValue> historyValues = parameterValueService.getHistoryValuesByParamName(paramName);
            return AjaxResult.success(historyValues);

        } catch (Exception e) {
            log.error("获取参数历史值失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 自动带出参数集
     */
    @ApiOperation("自动带出参数集")
    @GetMapping("/auto-suggest")
    public AjaxResult autoSuggestParamSet(
            @ApiParam("模板ID") @RequestParam Long templateId,
            @ApiParam("产品名称") @RequestParam String productName) {

        try {
            Long paramSetId = paramService.findParamSetByProductName(templateId, productName);
            return AjaxResult.success("查询成功", paramSetId);

        } catch (Exception e) {
            log.error("自动带出参数集失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
}
