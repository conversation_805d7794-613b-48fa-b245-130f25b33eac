package com.ruoyi.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.contract.domain.MasterContract;
import com.ruoyi.contract.service.ContractManagementService;
import com.ruoyi.web.taskcenter.util.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 合同管理Controller (合同台账管理)
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Api(tags = "合同管理")
@Slf4j
@RestController
@RequestMapping("/api/master-contracts")
@RequiredArgsConstructor
public class ContractManagementController extends BaseController {

    private final ContractManagementService contractManagementService;

    /**
     * 分页查询合同管理列表
     */
    @ApiOperation("分页查询合同管理列表")
    @GetMapping
    public AjaxResult getMasterContractList(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") long size,
            @ApiParam("产品名称") @RequestParam(required = false) String productName,
            @ApiParam("合同名称") @RequestParam(required = false) String contractName) {

        try {
            IPage<Map<String, Object>> result = contractManagementService.listMasterContracts(
                current, size, productName, contractName);
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("查询合同管理列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取合同管理详情
     */
    @ApiOperation("获取合同管理详情")
    @GetMapping("/{id}")
    public AjaxResult getMasterContractDetail(@ApiParam("合同管理ID") @PathVariable Long id) {
        try {
            Map<String, Object> result = contractManagementService.getMasterContractDetail(id);
            if (result == null) {
                return AjaxResult.error("合同不存在");
            }
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("获取合同管理详情失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 更新合同管理记录
     */
    @ApiOperation("更新合同管理记录")
    @PutMapping("/{id}")
    public AjaxResult updateMasterContract(
            @ApiParam("合同管理ID") @PathVariable Long id,
            @ApiParam("合同管理信息") @RequestBody @Validated MasterContract masterContract) {

        try {
            masterContract.setId(id);
            contractManagementService.updateMasterContract(masterContract);
            return AjaxResult.success("更新成功");

        } catch (Exception e) {
            log.error("更新合同管理记录失败", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }
}
