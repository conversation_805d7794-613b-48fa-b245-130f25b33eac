package com.ruoyi.contract.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合同文件版本历史实体类 (用于生成合同的版本管理)
 * 对应表：con_contract_doc_version_history
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("con_contract_doc_version_history")
public class ContractVersionHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的生成合同ID
     */
    @TableField("contract_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;

    /**
     * 版本号, e.g., 1.0, 1.1
     */
    @TableField("version")
    private String version;

    /**
     * 该版本文件在对象存储的路径
     */
    @TableField("storage_path")
    private String storagePath;

    /**
     * 编辑人ID
     */
    @TableField(value = "editor_id", fill = FieldFill.INSERT)
    private Long editorId;

    @TableField(value = "editor_name", fill = FieldFill.INSERT)
    private String editorName;

    /**
     * 编辑时间
     */
    @TableField(value = "edited_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime editedTime;
}
