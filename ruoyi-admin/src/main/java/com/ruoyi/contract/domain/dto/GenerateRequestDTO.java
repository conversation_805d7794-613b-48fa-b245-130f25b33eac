package com.ruoyi.contract.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 合同生成请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class GenerateRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 参数集ID
     */
    @NotBlank(message = "参数集ID不能为空")
    private String parameterSetId;
}
