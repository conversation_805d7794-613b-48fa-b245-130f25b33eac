package com.ruoyi.contract.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 保存合同DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class SaveContractDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模板版本ID
     */
    @NotNull(message = "模板版本ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateVersionId;

    /**
     * 产品代码
     */
    @NotBlank(message = "产品代码不能为空")
    private String productCode;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 投资经理
     */
    private String investmentManager;

    /**
     * 产品经理
     */
    private String productManager;

    /**
     * 参数集ID
     */
    @NotNull(message = "参数集ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parameterSetId;

    /**
     * 用户最终确认的合同名称
     */
    @NotBlank(message = "合同名称不能为空")
    private String contractName;

    /**
     * 临时文件路径
     */
    @NotBlank(message = "临时文件路径不能为空")
    private String tempFilePath;
}
