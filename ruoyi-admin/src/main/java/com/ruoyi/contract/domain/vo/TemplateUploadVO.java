package com.ruoyi.contract.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 模板上传响应VO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class TemplateUploadVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 上传的文件路径
     */
    private String filePath;

    /**
     * 解析出的参数列表
     */
    private List<String> detectedParams;

    /**
     * 原始文件名
     */
    private String originalFileName;
}
