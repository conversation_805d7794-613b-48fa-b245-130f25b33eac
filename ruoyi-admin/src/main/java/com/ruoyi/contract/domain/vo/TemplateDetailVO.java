package com.ruoyi.contract.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 模板详情响应VO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class TemplateDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板分类
     */
    private String templateCategory;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 创建人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 当前版本文件路径
     */
    private String currentFilePath;

    /**
     * 指向当前最新模板版本的ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long currentVersionId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 识别出的参数列表
     */
    private List<String> detectedParams;
}
