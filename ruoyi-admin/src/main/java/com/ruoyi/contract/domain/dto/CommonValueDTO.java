package com.ruoyi.contract.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 公共值DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Data
public class CommonValueDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID (更新时需要)
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 归属产品类型
     */
    @Size(max = 100, message = "产品类型长度不能超过100个字符")
    private String productType;

    /**
     * 归属模板分类
     */
    @Size(max = 100, message = "模板分类长度不能超过100个字符")
    private String templateCategory;

    /**
     * 公共值名称
     */
    @NotBlank(message = "公共值名称不能为空")
    @Size(max = 255, message = "公共值名称长度不能超过255个字符")
    private String name;

    /**
     * 公共值内容 (对于简单文本) 或 文件路径 (对于文件)
     */
    private String valueContent;

    /**
     * 公共值类型: FILE (文件路径) 或 TEXT (纯文本)
     */
    @NotBlank(message = "公共值类型不能为空")
    private String valueType;

    /**
     * HTML内容，用于预览显示
     */
    private String htmlContent;

    /**
     * 备注
     */
    private String remarks;
}
