package com.ruoyi.contract.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 参数集DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class ParamSetDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数集唯一标识 (由用户输入)
     */
    @NotBlank(message = "参数集编码不能为空")
    private String setCode;

    /**
     * 关联的模板ID
     */
    @NotNull(message = "模板ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 参数列表
     */
    private List<ParamItemDTO> params;
}
