package com.ruoyi.contract.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公共值实体类
 * 对应表：con_common_value
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("con_common_value")
public class CommonValue implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 归属产品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 归属模板分类
     */
    @TableField("template_category")
    private String templateCategory;

    /**
     * 公共值名称
     */
    @TableField("name")
    private String name;

    /**
     * 公共值内容 (对于简单文本) 或 文件路径 (对于文件)
     */
    @TableField("value_content")
    private String valueContent;

    /**
     * 公共值类型: FILE (文件路径) 或 TEXT (纯文本)
     */
    @TableField("value_type")
    private String valueType;

    /**
     * HTML内容，用于预览显示
     */
    @TableField("html_content")
    private String htmlContent;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建人ID
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人姓名
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 公共值类型常量
     */
    public static final String VALUE_TYPE_FILE = "FILE";
    public static final String VALUE_TYPE_TEXT = "TEXT";
}
