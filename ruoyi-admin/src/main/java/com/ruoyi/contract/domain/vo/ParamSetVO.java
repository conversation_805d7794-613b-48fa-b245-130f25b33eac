package com.ruoyi.contract.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.contract.domain.dto.ParamItemDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 参数集VO（包含参数列表）
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Data
public class ParamSetVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花算法生成的ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 参数集唯一标识 (由用户输入)
     */
    private String setCode;

    /**
     * 关联的模板ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 参数列表
     */
    private List<ParamItemDTO> params;
}
