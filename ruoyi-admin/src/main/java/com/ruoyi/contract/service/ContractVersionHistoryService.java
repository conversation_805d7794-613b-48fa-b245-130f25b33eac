package com.ruoyi.contract.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.contract.domain.ContractVersionHistory;
import com.ruoyi.contract.mapper.ContractVersionHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同版本历史服务
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
public class ContractVersionHistoryService extends ServiceImpl<ContractVersionHistoryMapper, ContractVersionHistory> {

    /**
     * 根据合同ID获取版本历史
     * 
     * @param contractId 合同ID
     * @return 版本历史列表
     */
    public List<ContractVersionHistory> getVersionsByContractId(Long contractId) {
        LambdaQueryWrapper<ContractVersionHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractVersionHistory::getContractId, contractId)
                   .orderByDesc(ContractVersionHistory::getEditedTime);
        return list(queryWrapper);
    }

    /**
     * 获取合同的最新版本
     * 
     * @param contractId 合同ID
     * @return 最新版本
     */
    public ContractVersionHistory getLatestVersion(Long contractId) {
        LambdaQueryWrapper<ContractVersionHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractVersionHistory::getContractId, contractId)
                   .orderByDesc(ContractVersionHistory::getEditedTime)
                   .last("LIMIT 1");
        return getOne(queryWrapper);
    }
}
