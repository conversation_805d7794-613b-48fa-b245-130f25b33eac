package com.ruoyi.contract.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.contract.domain.ContractTemplate;
import com.ruoyi.contract.domain.TemplateVersionHistory;
import com.ruoyi.contract.domain.dto.TemplateSaveDTO;
import com.ruoyi.contract.domain.dto.TemplateUploadDTO;
import com.ruoyi.contract.domain.vo.TemplateDetailVO;
import com.ruoyi.contract.domain.vo.TemplateUploadVO;
import com.ruoyi.contract.mapper.ContractTemplateMapper;
import com.ruoyi.contract.utils.DocxUtils;
import com.ruoyi.contract.utils.OssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 合同模板服务
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateService extends ServiceImpl<ContractTemplateMapper, ContractTemplate> {

    private final OssService ossService;
    private final TemplateVersionHistoryService templateVersionHistoryService;

    /**
     * 上传模板文件并解析参数
     *
     * @param file 模板文件
     * @return 上传结果
     */
    public TemplateUploadVO uploadTemplateFile(MultipartFile file) {
        try {
            // 1. 上传文件到OSS
            String filePath = ossService.uploadFile(
                file.getInputStream(),
                file.getOriginalFilename(),
                "templates"
            );

            // 2. 识别模板中的参数
            List<String> detectedParams = DocxUtils.detectPlaceholders(file.getInputStream());

            // 3. 构建返回结果
            TemplateUploadVO uploadVO = new TemplateUploadVO();
            uploadVO.setFilePath(filePath);
            uploadVO.setDetectedParams(detectedParams);
            uploadVO.setOriginalFileName(file.getOriginalFilename());

            log.info("模板文件上传成功，路径: {}, 识别参数: {}", filePath, detectedParams);
            return uploadVO;

        } catch (IOException e) {
            log.error("模板文件上传失败", e);
            throw new RuntimeException("模板文件上传失败", e);
        }
    }

    /**
     * 保存模板数据
     *
     * @param dto 模板保存DTO
     * @return 模板ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveTemplate(TemplateSaveDTO dto) {
        try {
            ContractTemplate template;
            String version;

            // 1. 判断是新模板还是现有模板的新版本
            if (dto.getTemplateId() != null) {
                // 为现有模板保存新版本
                template = getById(dto.getTemplateId());
                if (template == null) {
                    throw new RuntimeException("模板不存在");
                }
                // 获取最新版本号并递增
                version = getNextVersion(dto.getTemplateId());
            } else {
                // 创建全新模板
                template = new ContractTemplate();
                template.setTemplateName(dto.getTemplateName());
                template.setTemplateCategory(dto.getTemplateCategory());
                template.setProductType(dto.getProductType());
                template.setRemarks(dto.getRemarks());
                // TODO: 设置创建人信息
                // template.setCreatorId(getCurrentUserId());
                // template.setCreatorName(getCurrentUserName());

                save(template);
                version = "1.0";
            }

            // 2. 创建版本历史记录
            TemplateVersionHistory versionHistory = new TemplateVersionHistory();
            versionHistory.setTemplateId(template.getId());
            versionHistory.setVersion(version);
            versionHistory.setStoragePath(dto.getFilePath());
            versionHistory.setDetectedParams(JSONUtil.toJsonStr(dto.getDetectedParams()));
            versionHistory.setUploaderId(LoginHelper.getUserId());

            templateVersionHistoryService.save(versionHistory);

            // 3. 更新模板主记录的当前版本ID和文件路径
            template.setCurrentVersionId(versionHistory.getId());
            template.setTemplateName(dto.getTemplateName());
            template.setTemplateCategory(dto.getTemplateCategory());
            template.setProductType(dto.getProductType());
            template.setRemarks(dto.getRemarks());
            template.setCurrentFilePath(dto.getFilePath());
            updateById(template);

            log.info("模板保存成功，ID: {}, 版本: {}, 参数: {}", template.getId(), version, dto.getDetectedParams());
            return template.getId();

        } catch (Exception e) {
            log.error("模板保存失败", e);
            throw new RuntimeException("模板保存失败", e);
        }
    }

    /**
     * 分页查询模板列表
     *
     * @param current 当前页
     * @param size 页大小
     * @param templateName 模板名称（可选）
     * @param templateCategory 模板分类（可选）
     * @return 分页结果
     */
    public IPage<ContractTemplate> getTemplateList(long current, long size,
                                                   String templateName, String templateCategory) {
        Page<ContractTemplate> page = new Page<>(current, size);

        LambdaQueryWrapper<ContractTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(templateName != null, ContractTemplate::getTemplateName, templateName)
                   .eq(templateCategory != null, ContractTemplate::getTemplateCategory, templateCategory)
                   .orderByDesc(ContractTemplate::getCreatedTime);

        return page(page, queryWrapper);
    }

    /**
     * 获取模板详情
     *
     * @param id 模板ID
     * @return 模板详情
     */
    public ContractTemplate getTemplateDetail(Long id) {
        ContractTemplate template = getById(id);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }
        return template;
    }

    /**
     * 获取模板详情（包含参数列表）
     *
     * @param id 模板ID
     * @return 模板详情VO
     */
    public TemplateDetailVO getTemplateDetailWithParams(Long id) {
        ContractTemplate template = getById(id);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        TemplateDetailVO detailVO = new TemplateDetailVO();
        // 复制基本信息
        detailVO.setId(template.getId());
        detailVO.setTemplateName(template.getTemplateName());
        detailVO.setTemplateCategory(template.getTemplateCategory());
        detailVO.setProductType(template.getProductType());
        detailVO.setCreatorId(template.getCreatorId());
        detailVO.setCreatorName(template.getCreatorName());
        detailVO.setRemarks(template.getRemarks());
        detailVO.setCurrentFilePath(template.getCurrentFilePath());
        detailVO.setCurrentVersionId(template.getCurrentVersionId());
        detailVO.setCreatedTime(template.getCreatedTime());
        detailVO.setUpdatedTime(template.getUpdatedTime());

        // 获取参数列表
        List<String> detectedParams = new ArrayList<>();
        if (template.getCurrentVersionId() != null) {
            TemplateVersionHistory currentVersion = templateVersionHistoryService.getById(template.getCurrentVersionId());
            if (currentVersion != null && currentVersion.getDetectedParams() != null) {
                try {
                    detectedParams = JSONUtil.toList(currentVersion.getDetectedParams(), String.class);
                } catch (Exception e) {
                    log.warn("解析模板参数失败，模板ID: {}, 参数JSON: {}", id, currentVersion.getDetectedParams(), e);
                }
            }
        }
        detailVO.setDetectedParams(detectedParams);

        return detailVO;
    }

    /**
     * 修改模板元数据
     *
     * @param templateId 模板ID
     * @param dto 更新信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplate(Long templateId, TemplateUploadDTO dto) {
        ContractTemplate template = getById(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        template.setTemplateName(dto.getTemplateName());
        template.setTemplateCategory(dto.getTemplateCategory());
        template.setProductType(dto.getProductType());
        template.setRemarks(dto.getRemarks());

        updateById(template);
        log.info("模板更新成功，ID: {}", templateId);
    }

    /**
     * 查询模板版本历史
     *
     * @param templateId 模板ID
     * @return 版本历史列表
     */
    public List<TemplateVersionHistory> listTemplateVersions(Long templateId) {
        return templateVersionHistoryService.getVersionsByTemplateId(templateId);
    }

    /**
     * 删除模板
     *
     * @param id 模板ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTemplate(Long id) {
        ContractTemplate template = getById(id);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        // 删除所有版本的OSS文件
        List<TemplateVersionHistory> versions = templateVersionHistoryService.getVersionsByTemplateId(id);
        for (TemplateVersionHistory version : versions) {
            ossService.deleteFile(version.getStoragePath());
        }

        // 删除版本历史记录（外键约束会自动删除）
        // 删除模板主记录
        removeById(id);

        log.info("模板删除成功，ID: {}", id);
    }

    /**
     * 获取下一个版本号
     *
     * @param templateId 模板ID
     * @return 下一个版本号
     */
    private String getNextVersion(Long templateId) {
        List<TemplateVersionHistory> versions = templateVersionHistoryService.getVersionsByTemplateId(templateId);
        if (versions.isEmpty()) {
            return "1.0";
        }

        // 获取最新版本号并递增
        String latestVersion = versions.get(0).getVersion();
        try {
            BigDecimal version = new BigDecimal(latestVersion);
            BigDecimal increment = new BigDecimal("0.1");
            return version.add(increment).setScale(1, RoundingMode.HALF_UP).toString();
        } catch (Exception e) {
            // 如果解析失败，默认返回1.1
            return "1.1";
        }
    }
}
