package com.ruoyi.contract.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;

import java.io.*;
import java.util.*;

@Slf4j
public class DocxReportGenerator {

    public static void main(String[] args) {

        List<String> assetData = new ArrayList<>();
        assetData.add("银行存款");
        assetData.add("应收申购款");
        assetData.add("应收利息");
        assetData.add("其他应收款");
        assetData.add("负债合计");
        assetData.add("资产净值");

        // --- 2. 处理 Word 模板 ---
        String templatePath = "document.docx";
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (InputStream resourceAsStream = DocxReportGenerator.class.getClassLoader().getResourceAsStream(templatePath);
             XWPFDocument document = new XWPFDocument(Objects.requireNonNull(resourceAsStream))) {
            // --- 填充表格 ---
            // 假设文档中只有一个表格，如果不是，需要更复杂的逻辑来定位
            if (!document.getTables().isEmpty()) {
                XWPFTable table = document.getTables().get(0);
                fillTableWithData(table, assetData);
            } else {
                System.err.println("错误：在模板文件中未找到任何表格！");
            }
            document.write(out);
            FileOutputStream fileOutputStream = new FileOutputStream("result.docx");
            fileOutputStream.write(out.toByteArray());
            fileOutputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static byte[] generateReport(List<String> assetData) {
        String templatePath = "document.docx";
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (InputStream resourceAsStream = DocxReportGenerator.class.getClassLoader().getResourceAsStream(templatePath);
             XWPFDocument document = new XWPFDocument(Objects.requireNonNull(resourceAsStream))) {
            // --- 填充表格 ---
            // 假设文档中只有一个表格，如果不是，需要更复杂的逻辑来定位
            if (!document.getTables().isEmpty()) {
                XWPFTable table = document.getTables().get(0);
                fillTableWithData(table, assetData);
            } else {
                System.err.println("错误：在模板文件中未找到任何表格！");
            }
            document.write(out);
            return out.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 动态填充表格数据
     * @param table 要填充的表格
     * @param data 要填充的数据 (Key: 科目, Value: 金额)
     */
    public static void fillTableWithData(XWPFTable table, List<String> data) {
        for (String datum : data) {
            XWPFTableRow newRow = table.createRow(); // 在表格末尾创建新行
            // 设置第一列：科目名称 (并设置格式)
            formatCell(newRow.getCell(0), datum, ParagraphAlignment.LEFT);
            // 设置第二列：金额 (并设置格式)
            formatCell(newRow.getCell(1), "{{" + datum + "}}", ParagraphAlignment.RIGHT);
        }

        // 自动调整表格宽度，使其适应页面
        table.setWidth("100%");
    }

    /**
     * 统一设置单元格格式的辅助方法 (与之前版本相同)
     */
    private static void formatCell(XWPFTableCell cell, String text, ParagraphAlignment align) {
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        // 清除单元格中的默认段落内容
        if (!cell.getParagraphs().isEmpty()) {
            cell.removeParagraph(0);
        }

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(align);

        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setBold(false);
        run.setFontFamily("宋体");
        run.setFontSize(12);
    }
}
