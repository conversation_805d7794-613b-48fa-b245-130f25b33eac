package com.ruoyi.web.taskcenter.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.taskcenter.domain.ConditionTaskDTO;
import com.ruoyi.web.taskcenter.domain.OpsTaskAttrBasic;
import com.ruoyi.web.taskcenter.service.OpsTaskAttrBasicService;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoService;
import com.ruoyi.web.taskcenter.util.MessageConstant;
import com.ruoyi.web.taskcenter.util.R;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 任务配置单元接口层
 */
@RestController
@RequestMapping("/taskUnit")
@RequiredArgsConstructor
public class TasksUnitController {

    private final OpsTaskAttrBasicService basicService;

    private final ISysUserService systemUserService;

    private final OpsTaskGenInfoService genInfoService;


    /**
     * 任务单元基础配置表查询
     *
     * @return IPage
     */
    @GetMapping("/listByPage")
    public R<Object> listPage(
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "taskName", required = false) String taskName,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "taskType", required = false) String taskType,
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "10") int pageSize
    ) {
        //查询条件组织
        LambdaQueryWrapper<OpsTaskAttrBasic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(id), OpsTaskAttrBasic::getId, id);
        queryWrapper.like(StringUtils.hasText(taskName), OpsTaskAttrBasic::getTaskName, taskName);
        queryWrapper.eq(Objects.nonNull(status), OpsTaskAttrBasic::getTaskStatus, status);
        queryWrapper.eq(StringUtils.hasText(taskType), OpsTaskAttrBasic::getTaskType, taskType);
        queryWrapper.orderByDesc(OpsTaskAttrBasic::getCreateTime);
        //权限过滤
        ConditionTaskDTO dto = genInfoService.taskSpecialAuthFilter(null);
        if (dto == null || (dto.getPostIds() == null && dto.getType() != 4)) {
            return R.data(new Page<OpsTaskAttrBasic>(page, pageSize));
        }
        if (dto.getType() != 4) {
            queryWrapper.in(OpsTaskAttrBasic::getOwnerOrgId, dto.getPostIds());
        }
        //分页条件组织
        Page<OpsTaskAttrBasic> pageCon = new Page<>(page, pageSize);
        IPage<OpsTaskAttrBasic> res = basicService.page(pageCon, queryWrapper);
        Map<String, String> mapping = systemUserService.findIdNameMapping();
        for (OpsTaskAttrBasic record : res.getRecords()) {
            if (mapping.containsKey(record.getCreateBy())) {
                record.setCreateBy(mapping.get(record.getCreateBy()));
            }
        }
        return R.data(res);
    }

    /**
     * 任务单元新增/修改
     *
     * @return str
     */
    @PostMapping("/save")
    public R<Object> taskConfSave(@RequestBody OpsTaskAttrBasic basic) {
        basicService.saveOrUpdate(basic);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 任务单元新增/修改
     *
     * @return str
     */
    @GetMapping("/list")
    public R<Object> taskUnitList() {
        ConditionTaskDTO dto = genInfoService.taskSpecialAuthFilter(null);
        if (dto == null || (dto.getPostIds() == null && dto.getType() != 4)) {
            return R.data(new ArrayList<>());
        }
        LambdaQueryWrapper<OpsTaskAttrBasic> queryWrapper = new LambdaQueryWrapper<>();
        if (dto.getType() != 4) {
            queryWrapper.in(OpsTaskAttrBasic::getOwnerOrgId, dto.getPostIds());
        }
        List<OpsTaskAttrBasic> res = basicService.list(queryWrapper);
        return R.data(res);
    }

    /**
     * 任务单元新增/修改
     *
     * @return str
     */
    @GetMapping("/copy")
    public R<Object> taskConfCopy(@RequestParam("id") String id) {
        OpsTaskAttrBasic basic = basicService.getById(id);
        if (Objects.isNull(basic)) {
            return R.fail("数据不存在");
        }
        basic.setId(null);
        basic.setTaskName(basic.getTaskName() + "_复制");
        basicService.save(basic);
        return R.success(MessageConstant.COPY_SUCCESS);
    }

    /**
     * 任务单元新增/修改
     *
     * @return str
     */
    @GetMapping("/getById")
    public R<Object> getById(@RequestParam("id") String id) {
        OpsTaskAttrBasic basic = basicService.getById(id);
        if (Objects.isNull(basic)) {
            return R.fail("数据不存在");
        }
        return R.data(basic);
    }

    /**
     * 任务单元删除
     *
     * @return str
     */
    @GetMapping("/delete")
    public R<Object> delete(@RequestParam("id") String id) {
        OpsTaskAttrBasic basic = basicService.getById(id);
        if (Objects.isNull(basic)) {
            return R.fail("数据不存在");
        }
        if (basic.getTaskStatus() == 1) {
            return R.fail("任务配置已上线,无法删除");
        }
        basicService.removeById(Long.parseLong(id));
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 任务上线下线   0未上线 1 上线
     *
     * @return str
     */
    @GetMapping("/changeStatus")
    @Transactional(rollbackFor = Exception.class)
    public R<Object> changeStatus(@RequestParam("id") String id, @RequestParam("status") Integer status) {
        LambdaUpdateWrapper<OpsTaskAttrBasic> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpsTaskAttrBasic::getTaskStatus, status).eq(OpsTaskAttrBasic::getId, id);
        basicService.update(updateWrapper);
        return R.success(MessageConstant.UPDATE_SUCCESS);
    }
}
