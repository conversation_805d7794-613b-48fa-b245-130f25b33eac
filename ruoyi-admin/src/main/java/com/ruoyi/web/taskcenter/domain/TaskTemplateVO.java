package com.ruoyi.web.taskcenter.domain;

import cn.hutool.core.bean.BeanUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaskTemplateVO extends OpsTaskTemplate {


    private String createByName;

    private String updateByName;


    public TaskTemplateVO convertVO(OpsTaskTemplate template, Map<String, String> mapping) {
        TaskTemplateVO vo = new TaskTemplateVO();
        BeanUtil.copyProperties(template, vo);
        vo.setCreateByName(mapping.get(vo.getCreateBy()));
        vo.setUpdateByName(mapping.get(vo.getUpdateBy()));
        return vo;
    }
}
