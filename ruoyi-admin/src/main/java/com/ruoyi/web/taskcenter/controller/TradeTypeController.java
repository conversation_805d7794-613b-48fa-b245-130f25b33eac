package com.ruoyi.web.taskcenter.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.web.taskcenter.domain.OpsTradeType;
import com.ruoyi.web.taskcenter.service.OpsTradeTypeService;
import com.ruoyi.web.taskcenter.util.MessageConstant;
import com.ruoyi.web.taskcenter.util.R;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2024/7/23 15:21
 */
@RestController
@RequestMapping("/tradeType")
@RequiredArgsConstructor
public class TradeTypeController {

    final private OpsTradeTypeService tradeTypeService;

    @GetMapping("/listByPage")
    public R<?> listUserByPage(@RequestParam(required = false) String name,
                               @RequestParam(required = false, defaultValue = "1") int page,
                               @RequestParam(required = false, defaultValue = "10") int pageSize
    ) {
        Page<OpsTradeType> pageEntity = new Page<>(page, pageSize);
        LambdaQueryWrapper<OpsTradeType> calendarWrapper = Wrappers.lambdaQuery();
        calendarWrapper.like(StrUtil.isNotEmpty(name), OpsTradeType::getName, name);
        calendarWrapper.orderByDesc(OpsTradeType::getOrderSort);
        IPage<OpsTradeType> iPage = tradeTypeService.page(pageEntity, calendarWrapper);
        return R.data(iPage);
    }

    @GetMapping("/list")
    public R<?> list() {
        return R.data(tradeTypeService.list(Wrappers.lambdaQuery(OpsTradeType.class).ne(OpsTradeType::getTypeVal,2).orderByDesc(OpsTradeType::getOrderSort)));
    }

    @PostMapping("/save")
    public R<?> save(@RequestBody OpsTradeType tradeType) {
        tradeTypeService.saveOrUpdate(tradeType);
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    @GetMapping("/detail")
    public R<?> detail(@RequestParam String id) {
        return R.data(tradeTypeService.getById(id));
    }

    @PostMapping("/delete")
    public R<?> delete(@RequestBody OpsTradeType tradeType) {
        tradeTypeService.removeById(tradeType.getId());
        return R.success(MessageConstant.DELETE_SUCCESS);
    }

    /**
     * 交易日类型校验
     */
    @GetMapping("/check")
    public R<?> test(@RequestParam String id) {
        return R.data(tradeTypeService.checkDateType(id));
    }


    @GetMapping("/workCheck")
    public R<Boolean> workCheck(@RequestParam(value = "date",required = false)String date){
        return R.data(tradeTypeService.checkWorkdayByToday());
    }
}
