package com.ruoyi.web.taskcenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.web.taskcenter.domain.OpsTaskJobRelation;
import com.ruoyi.web.taskcenter.mapper.OpsTaskJobRelationMapper;
import com.ruoyi.web.taskcenter.service.OpsTaskJobRelationService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @description 针对表【ops_task_job_relation】的数据库操作Service实现
 * @createDate 2024-07-05 15:39:29
 */
@Service
public class OpsTaskJobRelationServiceImpl extends ServiceImpl<OpsTaskJobRelationMapper, OpsTaskJobRelation>
        implements OpsTaskJobRelationService {

    @Override
    public void deleteDailyTaskForTemplateId(String id) {
        baseMapper.deleteTaskReplicaIdsByTemplateId(id);
    }
}




