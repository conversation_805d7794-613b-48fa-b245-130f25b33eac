package com.ruoyi.web.taskcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.web.taskcenter.domain.OpsTaskHandledRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OpsTaskHandledRecordService extends IService<OpsTaskHandledRecord> {
    void insertIgnoreBatch(@Param("list") List<String> list, @Param("type") Integer type);

}
