package com.ruoyi.web.taskcenter.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.taskcenter.domain.OpsTaskMessageTip;
import com.ruoyi.web.taskcenter.mapper.OpsTaskMessageTipMapper;
import com.ruoyi.web.taskcenter.service.OpsTaskMessageTipService;
import com.ruoyi.web.taskcenter.util.SecureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class OpsTaskMessageTipServiceImpl extends ServiceImpl<OpsTaskMessageTipMapper, OpsTaskMessageTip> implements OpsTaskMessageTipService {

//    @Autowired
//    private OpsSysOrgService opsSysOrgService;
    private final ISysUserService iSysUserService;

    private final SysDeptMapper sysDeptMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<OpsTaskMessageTip> getMessageTipToday() {
        List<OpsTaskMessageTip> opsTaskMessageTips = getMessageTipByUser();
        Date today = DateUtil.beginOfDay(new Date());
        List<OpsTaskMessageTip> resetList = new ArrayList<>();
        opsTaskMessageTips.forEach(e -> {
            if(e.getUpdateTime() != null && DateUtil.beginOfDay(e.getUpdateTime()).isBefore(today) && StringUtils.hasText(e.getIgnoreUserId())){
                e.setIgnoreUserId("");
                resetList.add(e);
            }
        });
        if(!resetList.isEmpty()){
            updateBatchById(resetList);
        }
        // && !DateUtil.beginOfDay(e.getCreateTime()).equals(today) 如果需要当天不提醒当天新增的提醒，在return中加入该条件
        return opsTaskMessageTips.stream().filter(e -> {
            List<String> ignoreUserIds = Arrays.asList(ObjectUtil.defaultIfNull(e.getIgnoreUserId(), "").split(","));
            return !ignoreUserIds.contains(SecureUtil.currentUserId());
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void ignoreMessageTipToday(){
        List<OpsTaskMessageTip> opsTaskMessageTips = getMessageTipByUser();
        opsTaskMessageTips.forEach(e -> {
            List<String> ignoreUserIds = StringUtils.hasText(e.getIgnoreUserId()) ?  Arrays.asList(e.getIgnoreUserId().split(",")) : new ArrayList<>();
            ignoreUserIds.add(SecureUtil.currentUserId());
            e.setIgnoreUserId(String.join(",", ignoreUserIds));
            e.setUpdateTime(new Date());
        });
        if(!opsTaskMessageTips.isEmpty()){
            updateBatchById(opsTaskMessageTips);
        }
    };

    private List<OpsTaskMessageTip> getMessageTipByUser(){
        String today = DateUtil.today();
        List<OpsTaskMessageTip> opsTaskMessageTips = baseMapper.selectList(new LambdaQueryWrapper<OpsTaskMessageTip>().and(w -> w.eq(OpsTaskMessageTip::getTipType,2).ge(OpsTaskMessageTip::getTipDate, today))
                .or(w -> w.eq(OpsTaskMessageTip::getTipType,1).eq(OpsTaskMessageTip::getTipDate, today)));
        SysUser currentOrg = iSysUserService.selectUserById(Long.valueOf(SecureUtil.currentUserId()));
        if(currentOrg == null){
            return new ArrayList<>();
        }
        List<SysDept> sysDeptList = sysDeptMapper.selectChildrenDeptById(currentOrg.getDeptId());
        List<Long> deptList = sysDeptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        deptList.add(currentOrg.getDeptId());
        return opsTaskMessageTips.stream().filter(e ->
                (e.getTaskOwnerType().equals("1") && deptList.stream().map(String::valueOf).collect(Collectors.toList()).contains(e.getTaskOwnerId())) || (e.getTaskOwnerType().equals("2") && e.getTaskOwnerId().equals(SecureUtil.currentUserId()))
        ).collect(Collectors.toList());
    }
}
