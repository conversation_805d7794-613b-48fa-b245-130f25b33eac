package com.ruoyi.web.taskcenter.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@TableName(value ="OPS_TASK_REMINDER")
@Accessors(chain = true)
@Data
public class OpsTaskReminder {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Long taskId;

    @TableField(fill = FieldFill.INSERT, insertStrategy = FieldStrategy.IGNORED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
