package com.ruoyi.web.taskcenter.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.web.taskcenter.domain.OpsTaskGenInfo;
import com.ruoyi.web.taskcenter.domain.OpsTaskTransferConf;
import com.ruoyi.web.taskcenter.domain.TaskTransferVO;
import com.ruoyi.web.taskcenter.mapper.OpsTaskTransferConfMapper;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoService;
import com.ruoyi.web.taskcenter.service.OpsTaskTransferConfService;
import com.ruoyi.web.taskcenter.util.SecureUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;


import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【OPS_TASK_TRANSFER_CONF(任务转派配置-未来日期自动转派)】的数据库操作Service实现
 * @createDate 2024-09-10 09:56:31
 */
@Service
public class OpsTaskTransferConfServiceImpl extends ServiceImpl<OpsTaskTransferConfMapper, OpsTaskTransferConf>
        implements OpsTaskTransferConfService {


    private final OpsTaskGenInfoService opsTaskGenInfoService;

    @Lazy
    @Autowired
    public OpsTaskTransferConfServiceImpl(OpsTaskGenInfoService opsTaskGenInfoService) {
        this.opsTaskGenInfoService = opsTaskGenInfoService;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean delayTransfer(TaskTransferVO taskTransferVO) {
        if (isEndTimeValid(taskTransferVO.getTranEndTime())) {
            List<OpsTaskTransferConf> opsTaskTransferConfList = createTaskTransferConfs(taskTransferVO);
            if (!opsTaskTransferConfList.isEmpty()) {
                saveBatch(opsTaskTransferConfList);
                return taskTransferVO.getImmediate();
            }
        }
        return true;
    }

    private boolean isEndTimeValid(Date tranEndTime) {
        if (tranEndTime == null) {
            return false;
        }
        String tranEndTimeStr = new SimpleDateFormat("yyyy-MM-dd").format(tranEndTime);
        return !tranEndTimeStr.isEmpty();
    }

    private List<OpsTaskTransferConf> createTaskTransferConfs(TaskTransferVO taskTransferVO) {

        List<String> ids = new ArrayList<>();
        if (!StringUtils.hasText(taskTransferVO.getTaskId())) {
            List<OpsTaskGenInfo> opsTaskGenInfoList = opsTaskGenInfoService.listByIds(taskTransferVO.getTaskIds());
            for (OpsTaskGenInfo opsTaskGenInfoItem : opsTaskGenInfoList) {
                ids.add(opsTaskGenInfoItem.getId().toString());
                ids = Stream.concat(ids.stream(), getChildIds(opsTaskGenInfoItem).stream()).collect(Collectors.toList());
            }
        } else {
            OpsTaskGenInfo opsTaskGenInfo = opsTaskGenInfoService.getById(taskTransferVO.getTaskId());
            ids.add(opsTaskGenInfo.getId().toString());
            ids = Stream.concat(ids.stream(), getChildIds(opsTaskGenInfo).stream()).collect(Collectors.toList());
        }
        return createTaskTransferConf(ids, taskTransferVO);
    }

    private List<String> getChildIds(OpsTaskGenInfo opsTaskGenInfo) {
        if (StringUtils.hasText(opsTaskGenInfo.getTaskChildIds())) {
            return Arrays.stream(opsTaskGenInfo.getTaskChildIds().split(",")).collect(Collectors.toList());
        } else return new ArrayList<>();
    }

    private List<OpsTaskTransferConf> createTaskTransferConf(List<String> ids, TaskTransferVO taskTransferVO) {
        List<OpsTaskTransferConf> opsTaskTransferConfList = new ArrayList<>();
        List<OpsTaskGenInfo> opsTaskGenInfoList = opsTaskGenInfoService.listByIds(ids);
        for (OpsTaskGenInfo opsTaskGenInfoItem : opsTaskGenInfoList) {
            OpsTaskTransferConf opsTaskTransferConf = new OpsTaskTransferConf();
            opsTaskTransferConf.setTaskUnitId(Long.valueOf(opsTaskGenInfoItem.getTaskRefId()));
            opsTaskTransferConf.setCreateTime(new Date());
            opsTaskTransferConf.setTrStartTime(taskTransferVO.getTranStartTime());
            opsTaskTransferConf.setTrEndTime(taskTransferVO.getTranEndTime());

            Map<String, String> map = new HashMap<>();
            map.put("orgId", taskTransferVO.getOrgId());
            map.put("orgName", taskTransferVO.getOrgName());
            map.put("type", taskTransferVO.getType());
            map.put("taskDesc", taskTransferVO.getTaskDesc());
            map.put("userId", taskTransferVO.getUserId());
            map.put("userName", taskTransferVO.getUserName());
            map.put("currentUserId", SecureUtil.currentUserId());
            map.put("transferType", taskTransferVO.getTransferType());

            opsTaskTransferConf.setTrContent(JSONUtil.toJsonStr(map));
            opsTaskTransferConf.setCreateBy(SecureUtil.currentUserId());
            opsTaskTransferConfList.add(opsTaskTransferConf);
        }

        return opsTaskTransferConfList;
    }
}




