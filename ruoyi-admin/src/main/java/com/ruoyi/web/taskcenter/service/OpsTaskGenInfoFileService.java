package com.ruoyi.web.taskcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.web.taskcenter.domain.OpsTaskGenInfoFile;
import com.ruoyi.web.taskcenter.util.R;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;


import java.util.List;

public interface OpsTaskGenInfoFileService extends IService<OpsTaskGenInfoFile> {
    List<OpsTaskGenInfoFile> getListById(Long id);

    R<Object> uploadFile(Long infoId, MultipartFile file);


    R<Object> uploadFileReturnInfo(Long infoId, String indicatorId, MultipartFile file,String date);

    ResponseEntity<?> downLoad(Long id);

    String getByPath(String taskId, String indicatorId, String start, String end);

}
