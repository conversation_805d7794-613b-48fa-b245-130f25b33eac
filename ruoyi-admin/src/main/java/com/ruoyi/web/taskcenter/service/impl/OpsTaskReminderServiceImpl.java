package com.ruoyi.web.taskcenter.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.web.taskcenter.domain.ConditionTaskDTO;
import com.ruoyi.web.taskcenter.domain.OpsTaskReminder;
import com.ruoyi.web.taskcenter.mapper.OpsTaskReminderMapper;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoService;
import com.ruoyi.web.taskcenter.service.OpsTaskReminderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OpsTaskReminderServiceImpl extends ServiceImpl<OpsTaskReminderMapper, OpsTaskReminder> implements OpsTaskReminderService {

    @Lazy
    @Autowired
    private OpsTaskGenInfoService opsTaskGenInfoService;

    @Override
    public List<String> getReminder(Boolean isLeader) {
        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if(condition==null){
            return new ArrayList<>();
        }
        return baseMapper.getReminder(condition, DateUtil.today(), isLeader);
    }
}
