package com.ruoyi.web.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.taskcenter.domain.OpsTaskHandledRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OpsTaskHandledRecordMapper extends BaseMapper<OpsTaskHandledRecord> {

    int insertIgnoreBatch(@Param("list") List<String> list, @Param("type") Integer type);

}
