package com.ruoyi.web.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.taskcenter.domain.ConditionTaskDTO;
import com.ruoyi.web.taskcenter.domain.OpsTaskReminder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OpsTaskReminderMapper extends BaseMapper<OpsTaskReminder> {

    List<String> getReminder(@Param("dc") ConditionTaskDTO condition, @Param("today") String today, @Param("isLeader") Boolean isLeader);
}
