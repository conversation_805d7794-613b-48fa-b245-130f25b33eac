package com.ruoyi.web.taskcenter.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

@TableName(value = "OPS_TASK_MESSAGE_TIP")
@Data
public class OpsTaskMessageTip {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String tipDate;

    private String message;

    private Date createTime;

    private String ignoreUserId;

    private String taskOwnerId;

    private String taskOwnerType;

    /***
     * 1 当日 2 范围
     */
    private Integer tipType;

    private Date updateTime;
}
