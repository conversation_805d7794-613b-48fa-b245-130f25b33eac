package com.ruoyi.web.taskcenter.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TaskScriptResultVO implements Serializable {

    /**
     * 状态,true任务开启\false任务未开启
     */
    private Boolean status = false;

    /**
     * 任务开始时间
     */
    private String startTime;

    /**
     * 任务结束时间
     */
    private String endTime;

    /**
     * 工作量计数
     */
    private Integer count;

    //脚本指标回传的任务名称
    private String  taskName;

    private String taskType;

    //脚本指标根据任务名称返回的批量命中情况
    private List<TaskScriptResultVO> dureTaskList;
}
