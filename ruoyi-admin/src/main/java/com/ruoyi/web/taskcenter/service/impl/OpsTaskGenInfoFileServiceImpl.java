package com.ruoyi.web.taskcenter.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.web.taskcenter.domain.OpsTaskGenInfoFile;
import com.ruoyi.web.taskcenter.mapper.OpsTaskGenInfoFileMapper;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoFileService;
import com.ruoyi.web.taskcenter.service.OpsTradeTypeService;
import com.ruoyi.web.taskcenter.util.R;
import com.ruoyi.web.taskcenter.util.SecureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class OpsTaskGenInfoFileServiceImpl extends ServiceImpl<OpsTaskGenInfoFileMapper, OpsTaskGenInfoFile>
        implements OpsTaskGenInfoFileService {
    @Value("${ruoyi.profile}")
    private  String uploadUrl;


    private final OpsTradeTypeService opsTradeTypeService;

    @Override
    public List<OpsTaskGenInfoFile> getListById(Long id) {
        return this.baseMapper.selectListByInfoId(id);
    }



    @Override
    public R<Object> uploadFile(Long infoId, MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("文件为空");
        }
        OpsTaskGenInfoFile opsTaskGenInfoFile = new OpsTaskGenInfoFile();
        opsTaskGenInfoFile.setInfoId(infoId);
        opsTaskGenInfoFile.setUserId(SecureUtil.currentUserId());
        opsTaskGenInfoFile.setUserName(SecureUtil.currentUserName());
        opsTaskGenInfoFile.setName(file.getOriginalFilename());
        opsTaskGenInfoFile.setFilePath(uploadUrl+ UUID.randomUUID()+"/");
        opsTaskGenInfoFile.setDateTime(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        this.upload(file,opsTaskGenInfoFile.getFilePath());

        return R.status(this.baseMapper.insert(opsTaskGenInfoFile) > 0);
    }

    @Override
    public R<Object> uploadFileReturnInfo(Long infoId, String indicatorId, MultipartFile file,String date) {
        if (file.isEmpty()) {
            return R.fail("文件为空");
        }
        OpsTaskGenInfoFile opsTaskGenInfoFile = new OpsTaskGenInfoFile();
        opsTaskGenInfoFile.setInfoId(infoId);
        opsTaskGenInfoFile.setUserId(SecureUtil.currentUserId());
        opsTaskGenInfoFile.setUserName(SecureUtil.currentUserName());
        opsTaskGenInfoFile.setName(file.getOriginalFilename());
        opsTaskGenInfoFile.setFilePath(uploadUrl+ UUID.randomUUID()+"/");
        opsTaskGenInfoFile.setDateTime(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        this.upload(file,opsTaskGenInfoFile.getFilePath());
        //触发脚本解析文件
        //opsTradeTypeService.triggerScriptExecute(indicatorId,opsTaskGenInfoFile.getFilePath()+opsTaskGenInfoFile.getName(),date);
        return R.status(this.baseMapper.insert(opsTaskGenInfoFile) > 0);
    }

    @Override
    public String getByPath(String taskId, String indicatorId, String start, String end) {

       // R<Object> res=opsTradeTypeService.triggerScriptExecuteForExport(indicatorId,start,end);
        return null;
    }

    @Override
    public ResponseEntity<?> downLoad(Long id) {

        OpsTaskGenInfoFile opsTaskGenInfoFile = this.baseMapper.selectById(id);
        if(ObjectUtil.isEmpty(opsTaskGenInfoFile)){
            return ResponseEntity.status(500).body(R.data("文件不存在"));
        }
        try {
            File file = new File(opsTaskGenInfoFile.getFilePath(), opsTaskGenInfoFile.getName());
            if (file.exists() && file.canRead()) {
//                InputStream inputStream = FileUtil.getInputStream(file);
//                byte[] fileBytes = IoUtil.readBytes(inputStream);
//                HttpHeaders headers = new HttpHeaders();
//                headers.add("content-type", "application/octet-stream");
//                if(FileUtil.getSuffix(opsTaskGenInfoFile.getName()).equals("xls")) {
//                    headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel"); // 对于 .xls 文件
//                }
//                if(FileUtil.getSuffix(opsTaskGenInfoFile.getName()).equals("xlsx")){
//                    headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"); // 对于 .xlsx 文件
//                }
//                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(opsTaskGenInfoFile.getName(), StandardCharsets.UTF_8).replace("+", "%20"));
//                return ResponseEntity.ok()
//                        .headers(headers)
//                        .body(fileBytes);
                FileSystemResource resource = new FileSystemResource(file);

                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(opsTaskGenInfoFile.getName(), StandardCharsets.UTF_8));
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

                return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .body(resource);
            } else {
                return ResponseEntity.status(500).body(R.data("文件不存在"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).build();
        }
    }



    private void upload(MultipartFile file, String path)  {
        String fileName = file.getOriginalFilename();
        Path uploadPath = Paths.get(path+ fileName);
        File uploadDirectory = new File(path);
        if (!uploadDirectory.exists()) {
            uploadDirectory.mkdirs();
        }
        if (Files.exists(uploadPath)) {
            return;
        }
        try {
            file.transferTo(uploadPath.toFile());
            R.success("文件上传成功");
        }
        catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
