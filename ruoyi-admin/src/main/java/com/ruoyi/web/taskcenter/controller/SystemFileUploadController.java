package com.ruoyi.web.taskcenter.controller;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.web.taskcenter.domain.OpsTaskGenInfoFile;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoFileService;
import com.ruoyi.web.taskcenter.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
public class SystemFileUploadController {


    private final OpsTaskGenInfoFileService opsTaskGenInfoFileService;

    /**
     * 获取当前信息上传附件列表
     *
     * @param taskId
     * @return
     */
    @GetMapping("/fileList")
    public R<List<OpsTaskGenInfoFile>> fileList(@RequestParam("taskId") Long taskId) {
        return R.data(opsTaskGenInfoFileService.getListById(taskId));
    }

    /**
     * 获取当前信息上传附件数量
     *
     * @param taskId
     * @return
     */
    @GetMapping("/fileCount")
    public R<Integer> fileCount(@RequestParam("taskId") Long taskId) {
        return R.data(opsTaskGenInfoFileService.getListById(taskId).size());
    }

    /**
     * 删除附件
     *
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public R<Object> deleteFile(@RequestParam("id") Long id) {
        return opsTaskGenInfoFileService.removeById(id) ? R.success("删除成功") : R.fail("删除失败");
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    @PostMapping("/file")
    public R<Object> uploadFile(@RequestParam("taskId") Long taskId, @RequestParam("file") MultipartFile file) {
        return opsTaskGenInfoFileService.uploadFile(taskId, file);
    }

    /**
     * 上传文件,并触发脚本,执行逻辑
     *
     * @param file 文件
     * @return bool
     */
    @PostMapping("/fileTrigger")
    public R<Object> uploadFileTrigger(@RequestParam("taskId") Long taskId,
                                       @RequestParam("indicatorId")String indicatorId,
                                       @RequestParam("file") MultipartFile file,@RequestParam(value = "date",required = false)String date) {
        return opsTaskGenInfoFileService.uploadFileReturnInfo(taskId, indicatorId,file,date);
    }

    /**
     * 下载
     *
     * @param id 附件id
     * @return stream
     */
    public ResponseEntity<?> downLoad(@RequestParam("id") Long id) {
        return opsTaskGenInfoFileService.downLoad(id);
    }

    @GetMapping("/download")
    public void download(@RequestParam("id") String id, HttpServletResponse response) {
        OpsTaskGenInfoFile opsTaskGenInfoFile = opsTaskGenInfoFileService.getById(id);
        if (ObjectUtil.isEmpty(opsTaskGenInfoFile)) {
            writeJsonResponse(response, R.fail("文件不存在"));
        }
        File file = new File(opsTaskGenInfoFile.getFilePath(), opsTaskGenInfoFile.getName());
        if (!file.exists() && file.canRead()) {
            writeJsonResponse(response, R.fail("文件不存在"));
        }
        try {
            // 设置响应头
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(file.getName(), StandardCharsets.UTF_8));
            byte[] buffer = new byte[1024];
            FileInputStream fis = null; //文件输入流
            BufferedInputStream bis = null;
            OutputStream os = null; //输出流
            os = response.getOutputStream();
            fis = new FileInputStream(file);
            bis = new BufferedInputStream(fis);

            int len = 0;
            while ((len = bis.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
            bis.close();
            fis.close();
        } catch (Exception e) {
            log.error("下载文件错误", e);
        }
    }

    @GetMapping("/downloadByIndicator")
    public void downloadByIndicator(@RequestParam("taskId")String taskId,@RequestParam("indicatorId")String indicatorId,
                                    @RequestParam("startDate")String start,@RequestParam("endDate") String end,
                                    HttpServletResponse response){
        //通过任务单元id获取绑定指标
        String path=opsTaskGenInfoFileService.getByPath(taskId,indicatorId,start,end);
        if(path!=null){
            File file = new File(path);
            if (!file.exists() && file.canRead()) {
                writeJsonResponse(response, R.fail("文件不存在"));
            }
            try {
                // 设置响应头
                response.setCharacterEncoding("UTF-8");
                response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(file.getName(), StandardCharsets.UTF_8));
                byte[] buffer = new byte[1024];
                FileInputStream fis = null; //文件输入流
                BufferedInputStream bis = null;
                OutputStream os = null; //输出流
                os = response.getOutputStream();
                fis = new FileInputStream(file);
                bis = new BufferedInputStream(fis);

                int len = 0;
                while ((len = bis.read(buffer)) > 0) {
                    os.write(buffer, 0, len);
                }
                bis.close();
                fis.close();
            } catch (Exception e) {
                log.error("任务单元-自定义配置按钮下载文件错误", e);
            }
        }
    }

    /**
     * URL编码
     *
     * @param url see note
     * @return see note
     */
    public static String encodeUrl(String url) {
        return URLEncoder.encode(url, StandardCharsets.UTF_8);
    }

    private final static ObjectMapper objectMapper = new ObjectMapper();

    public static void writeJsonResponse(HttpServletResponse response, Object data) {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        if(data instanceof R){
            R<Object> r= (R<Object>) data;
            if(r.getCode()==401){
                response.setStatus(401);
            }
        }
        try {
            String json = objectMapper.writeValueAsString(data);
            response.getWriter().write(json);
        } catch (IOException e) {
            // 处理异常
            log.error("登录接口响应异常", e);
        }
    }
}
