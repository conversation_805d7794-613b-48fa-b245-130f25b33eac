package com.ruoyi.web.taskcenter.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.web.taskcenter.domain.IndicatorInfo;
import com.ruoyi.web.taskcenter.domain.IndicatorInfoDTO;
import com.ruoyi.web.taskcenter.util.MessageConstant;
import com.ruoyi.web.taskcenter.util.R;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IndicatorInfoFeignHandler  {

   // private final IndicatorInfoService infoService;



    /***
     * feign实现进程内调用support
     * @param dto 请求内容
     * @return R<Object>
     */
    public R<Object> rpc(IndicatorInfoDTO dto) {
//        String id = dto.getId();
//        IndicatorInfo info = infoService.getById(id);
//        if (null == info || StrUtil.isEmptyIfStr(info.getType())) {
//            return R.fail(MessageConstant.PARAM_MISS);
//        }
//        BeanUtil.copyProperties(info, dto);
//        return infoService.executeUp(dto);
        return R.data("success");
    }

    public R<Object> onceExeGenSpecTask(String replicaId) {
      //   infoService.triggerOnceSpecTaskGen(replicaId);
         return R.success("执行完成");
    }

}
