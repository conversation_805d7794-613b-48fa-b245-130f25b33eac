<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shift.mapper.OpsSysArrangeMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.shift.domain.entity.OpsSysArrange">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="shiftId" column="shift_id" jdbcType="VARCHAR"/>
        <result property="date" column="date" jdbcType="VARCHAR"/>
        <result property="message" column="message" jdbcType="VARCHAR"/>
        <result property="changeDate" column="changeDate" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,ORG_ID,USER_ID,SHIFT_ID,DATE,CREATE_BY,
        CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <delete id="del">
        DELETE
        FROM OPS_SYS_ARRANGE
        WHERE OPS_SYS_ARRANGE.ORG_ID = #{opsSysArrange.orgId}
          AND OPS_SYS_ARRANGE.SHIFT_ID = #{opsSysArrange.shiftId}
          AND OPS_SYS_ARRANGE.DATE = #{opsSysArrange.date}
    </delete>

    <delete id="delByDate">
        DELETE
        FROM OPS_SYS_ARRANGE
        WHERE OPS_SYS_ARRANGE.DATE &gt;= #{opsSysArrangedDTO.startDate}
          AND OPS_SYS_ARRANGE.DATE &lt;= #{opsSysArrangedDTO.endDate}
          AND OPS_SYS_ARRANGE.ORG_ID = #{opsSysArrangedDTO.orgId}
          AND OPS_SYS_ARRANGE.SHIFT_ID = #{opsSysArrangedDTO.shiftId}
          AND OPS_SYS_ARRANGE.SHIFT_ID = #{opsSysArrangedDTO.userId}
    </delete>

    <select id="findByShiftId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM OPS_SYS_ARRANGE
        WHERE SHIFT_ID = #{id}
    </select>

    <select id="list" resultType="com.ruoyi.shift.domain.vo.OpsSysArrangeVO">
        SELECT
        OPS_SYS_ARRANGE.ID,
        OPS_SYS_ARRANGE.ORG_ID,
        OPS_SYS_ARRANGE.USER_ID,
        OPS_SYS_ARRANGE.SHIFT_ID,
        OPS_SYS_ARRANGE.DATE,
        OPS_SYS_ARRANGE.CREATE_BY,
        OPS_SYS_ARRANGE.CREATE_TIME,
        OPS_SYS_ARRANGE.UPDATE_BY,
        OPS_SYS_ARRANGE.UPDATE_TIME,
        OPS_SYS_SHIFT.NAME SHIFTNAME,
        OPS_SYS_SHIFT.SORT,
        su.user_name USERNAME
        FROM OPS_SYS_ARRANGE
        LEFT JOIN OPS_SYS_SHIFT ON OPS_SYS_ARRANGE.SHIFT_ID = OPS_SYS_SHIFT.ID
        LEFT JOIN sys_user su ON OPS_SYS_ARRANGE.USER_ID = su.user_id
        <where>
            <if test="id != null and id != ''">
                AND OPS_SYS_ARRANGE.ORG_ID = #{id}
            </if>
            <if test="startDate != null and startDate != ''">
                AND OPS_SYS_ARRANGE.DATE &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND OPS_SYS_ARRANGE.DATE &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="findByOrgShiftUserDate" resultType="java.lang.Integer">
        SELECT count(1)
        FROM OPS_SYS_ARRANGE
        WHERE OPS_SYS_ARRANGE.ORG_ID = #{opsSysArrange.orgId}
          AND OPS_SYS_ARRANGE.SHIFT_ID = #{opsSysArrange.shiftId}
          AND OPS_SYS_ARRANGE.USER_ID = #{opsSysArrange.userId}
          AND OPS_SYS_ARRANGE.DATE = #{opsSysArrange.date}
    </select>

    <select id="findByUserDate" resultType="com.ruoyi.shift.domain.vo.OpsSysArrangeVO">
        select osa.*, oss.NAME shiftName, sd.dept_name deptName, su.user_name userName
        from ops_sys_arrange osa
                 left join ops_sys_shift oss on osa.SHIFT_ID = oss.ID
                 left join sys_dept sd on osa.ORG_ID = sd.dept_id
                 left join sys_user su on su.user_id = osa.USER_ID
        where osa.`DATE` &gt;= #{weekStart}
          and osa.`DATE` &lt;= #{weekEnd}
          and osa.USER_ID = #{userId}
    </select>
    <select id="getChangeShiftToday" resultType="com.ruoyi.shift.domain.vo.OpsSysArrangeVO">
        select osa.*, oss.NAME shiftName, sd.dept_name deptName, su.user_name userName
        from ops_sys_arrange osa
                 left join ops_sys_shift oss on osa.SHIFT_ID = oss.ID
                 left join sys_dept sd on osa.ORG_ID = sd.dept_id
                 left join sys_user su on su.user_id = osa.USER_ID
        where osa.`DATE` = #{date}
          and osa.USER_ID = #{userId}
    </select>
    <select id="getChangeShiftLastDay" resultType="com.ruoyi.shift.domain.vo.OpsSysArrangeVO">
        select osa.*, oss.NAME shiftName, sd.dept_name deptName, su.user_name userName
        from ops_sys_arrange osa
                 left join ops_sys_shift oss on osa.SHIFT_ID = oss.ID
                 left join sys_dept sd on osa.ORG_ID = sd.dept_id
                 left join sys_user su on su.user_id = osa.USER_ID
        where osa.`DATE` = (select osa.`DATE`
                            from ops_sys_arrange osa
                            where osa.`DATE` &lt; #{date}
                            group by osa.`DATE`
                            order by osa.`DATE` desc
                            limit 0,1)
    </select>

</mapper>
