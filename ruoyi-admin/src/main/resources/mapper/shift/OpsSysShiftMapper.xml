<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shift.mapper.OpsSysShiftMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.shift.domain.entity.OpsSysShift">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="BIGINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,NAME,ORG_ID,REMARK,SORT,CREATE_BY,
        CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <select id="list" resultType="com.ruoyi.shift.domain.vo.OpsSysShiftVO">
        SELECT
        OPS_SYS_SHIFT.ID,
        OPS_SYS_SHIFT.NAME,
        OPS_SYS_SHIFT.ORG_ID,
        OPS_SYS_SHIFT.REMARK,
        OPS_SYS_SHIFT.CREATE_BY,
        OPS_SYS_SHIFT.CREATE_TIME,
        OPS_SYS_SHIFT.UPDATE_BY,
        OPS_SYS_SHIFT.UPDATE_TIME,
        OPS_SYS_SHIFT.SORT,
        sd.dept_name ORGNAME
        FROM OPS_SYS_SHIFT
        LEFT JOIN sys_dept sd ON OPS_SYS_SHIFT.ORG_ID = sd.dept_id
        <where>
            <if test="opsSysOrg.name != null and opsSysOrg.name != ''">
                AND NAME LIKE #{opsSysOrg.name}
            </if>
            <if test="opsSysOrg.remark != null and opsSysOrg.remark != ''">
                AND REMARK LIKE #{opsSysOrg.remark}
            </if>
        </where>
        ORDER BY OPS_SYS_SHIFT.SORT
    </select>

    <select id="getByOrgId" resultType="com.ruoyi.shift.domain.entity.OpsSysShift">
        SELECT OPS_SYS_SHIFT.ID,
               OPS_SYS_SHIFT.NAME,
               OPS_SYS_SHIFT.ORG_ID,
               OPS_SYS_SHIFT.REMARK,
               OPS_SYS_SHIFT.SORT,
               OPS_SYS_SHIFT.CREATE_BY,
               OPS_SYS_SHIFT.CREATE_TIME,
               OPS_SYS_SHIFT.UPDATE_BY,
               OPS_SYS_SHIFT.UPDATE_TIME
        FROM OPS_SYS_SHIFT
        WHERE OPS_SYS_SHIFT.ORG_ID = #{id}
        ORDER BY OPS_SYS_SHIFT.SORT
    </select>
    <select id="checkName" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM OPS_SYS_SHIFT
        WHERE NAME = #{opsSysShift.name}
        AND ID != #{opsSysShift.id}
    </select>

</mapper>
