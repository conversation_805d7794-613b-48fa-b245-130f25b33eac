<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.taskcenter.mapper.OpsTaskTemplateMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.web.taskcenter.domain.OpsTaskTemplate">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="templateName" column="template_name" jdbcType="VARCHAR"/>
            <result property="templateType" column="template_type" jdbcType="INTEGER"/>
            <result property="templateUse" column="template_use" jdbcType="INTEGER"/>
            <result property="templateStatus" column="template_status" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,template_name,template_type,
        template_use,template_status,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
    <delete id="removeRelations">
        delete from   ops_task_template_relation where template_id=#{id}
    </delete>
    <delete id="removeReplicaRelations">
       delete  from ops_task_attr_basic_replica  where id in (
           select task_replica_id from ops_task_template_relation b where b.template_id=#{id}
           )
    </delete>
    <select id="findAllByMultiCondition" resultType="com.ruoyi.web.taskcenter.domain.OpsTaskTemplate">
        select * from ops_task_template
    </select>
    <select id="listReplicaIds" resultType="java.lang.String">
        select b.task_replica_id from OPS_TASK_TEMPLATE_RELATION b where b.template_id =#{id}
    </select>
</mapper>
