<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.taskcenter.mapper.OpsTaskGenInfoFileMapper">
    <resultMap id="OpsTaskGenInfoFileResultMap" type="com.ruoyi.web.taskcenter.domain.OpsTaskGenInfoFile">
        <id column="ID" property="id" />
        <result column="INFO_ID" property="infoId" />
        <result column="NAME" property="name" />
        <result column="FILE_PATH" property="filePath" />
        <result column="DATE_TIME" property="dateTime" />
        <result column="USER_ID" property="userId" />
        <result column="USER_NAME" property="userName" />
    </resultMap>
    <sql id="Base_Column_List">
        ID,INFO_ID,NAME,FILE_PATH,DATE_TIME,USER_ID,USER_NAME
    </sql>
    <select id="selectListByInfoId" resultMap="OpsTaskGenInfoFileResultMap">
        select <include refid="Base_Column_List"/> from ops_task_gen_info_file
        where INFO_ID = #{id} order by date_time desc
    </select>
</mapper>