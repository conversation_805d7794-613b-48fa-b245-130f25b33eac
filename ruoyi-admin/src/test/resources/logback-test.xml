<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="console.log.pattern"
              value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- 关闭docx4j的调试日志 -->
    <logger name="org.docx4j" level="WARN"/>
    <logger name="org.docx4j.jaxb" level="ERROR"/>
    <logger name="org.docx4j.jaxb.NamespacePrefixMapperUtils" level="ERROR"/>
    <logger name="org.docx4j.model" level="ERROR"/>
    <logger name="org.docx4j.openpackaging" level="ERROR"/>
    
    <!-- 关闭其他可能的噪音日志 -->
    <logger name="org.apache.poi" level="WARN"/>
    <logger name="com.deepoove.poi" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    
    <!-- 保持我们的业务日志为INFO级别 -->
    <logger name="com.ruoyi.contract.service.DocxMergeService" level="INFO"/>

    <root level="INFO">
        <appender-ref ref="console" />
    </root>

</configuration>