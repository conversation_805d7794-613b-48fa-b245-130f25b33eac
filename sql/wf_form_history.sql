-- 流程表单历史数据表
CREATE TABLE `wf_form_history` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `process_instance_id` varchar(64) NOT NULL COMMENT '流程实例ID',
  `task_id` varchar(64) DEFAULT NULL COMMENT '任务ID',
  `task_name` varchar(255) DEFAULT NULL COMMENT '任务名称',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型（REVOKE-撤回，RETURN-退回）',
  `revoke_count` int(11) DEFAULT 1 COMMENT '撤回次数',
  `form_data` longtext COMMENT '表单数据（JSON格式）',
  `operator_id` varchar(64) DEFAULT NULL COMMENT '操作用户ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作用户名称',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_process_instance_id` (`process_instance_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程表单历史数据表';
