-- ----------------------------
-- 公共值管理表
-- ----------------------------
DROP TABLE IF EXISTS `con_common_value`;
CREATE TABLE `con_common_value` (
  `id` bigint(20) NOT NULL COMMENT '雪花算法生成的ID',
  `product_type` varchar(100) DEFAULT NULL COMMENT '归属产品类型',
  `template_category` varchar(100) DEFAULT NULL COMMENT '归属模板分类',
  `name` varchar(255) NOT NULL COMMENT '公共值名称',
  `value_content` longtext COMMENT '公共值内容 (对于简单文本) 或 文件路径 (对于文件)',
  `value_type` varchar(10) NOT NULL DEFAULT 'TEXT' COMMENT '公共值类型: FILE (文件路径) 或 TEXT (纯文本)',
  `html_content` longtext COMMENT 'HTML内容，用于预览显示',
  `remarks` text COMMENT '备注',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(64) DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_type` (`product_type`),
  KEY `idx_template_category` (`template_category`),
  KEY `idx_name` (`name`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公共值管理表';

-- 插入示例数据
INSERT INTO `con_common_value` (`id`, `product_type`, `template_category`, `name`, `value_content`, `value_type`, `html_content`, `remarks`, `creator_id`, `creator_name`) VALUES
(1, '基金产品', '合同模板', '标准条款1', '这是一个标准的合同条款内容，用于基金产品的合同模板中。', 'TEXT', NULL, '基金产品通用条款', 1, 'admin'),
(2, '理财产品', '协议模板', '风险提示', '投资有风险，理财需谨慎。本产品不保证本金安全，可能存在投资损失的风险。', 'TEXT', NULL, '理财产品风险提示条款', 1, 'admin'),
(3, '保险产品', '保单模板', '免责声明', '在以下情况下，保险公司不承担赔偿责任：1.投保人故意行为；2.被保险人犯罪行为；3.战争、军事行动等。', 'TEXT', NULL, '保险产品免责条款', 1, 'admin');
